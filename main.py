"""
主程序入口 - 启动录屏软件
"""

import os
import sys
import logging
import logging.handlers
import argparse
from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QIcon
from PyQt5.QtCore import Qt

import config
import utils
from gui import MainWindow
from hidden_mode import HiddenModeManager

# 设置日志
logger = logging.getLogger(__name__)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="功能强大的录屏软件")

    # 隐藏模式参数
    parser.add_argument("--hidden", action="store_true", help="以隐藏模式启动")
    parser.add_argument("--duration", type=int, help="录制时长（分钟）")
    parser.add_argument("--output", type=str, help="输出目录")

    # 其他参数
    parser.add_argument("--version", action="store_true", help="显示版本信息")

    return parser.parse_args()

def show_version():
    """显示版本信息"""
    version = "1.0.0"
    print(f"功能强大的录屏软件 v{version}")
    print("一个支持隐藏式录屏和多通用格式转换的录屏软件。")
    print("© 2023 All Rights Reserved")

def start_hidden_recording(duration_minutes=None, output_dir=None):
    """启动隐藏录制"""
    try:
        # 创建隐藏模式管理器
        hidden_manager = HiddenModeManager()

        # 启用隐藏模式
        hidden_manager.enable()

        # 设置输出目录
        if output_dir is None:
            output_dir = config.DEFAULT_OUTPUT_DIR

        # 开始录制
        if hidden_manager.start_recording(output_dir, duration_minutes):
            logger.info(f"已启动隐藏录制，输出目录: {output_dir}")

            if duration_minutes:
                logger.info(f"录制将在 {duration_minutes} 分钟后自动停止")

            # 保持程序运行
            print(f"隐藏录制已启动，输出目录: {output_dir}")
            if duration_minutes:
                print(f"录制将在 {duration_minutes} 分钟后自动停止")
            print("按 Ctrl+C 停止录制...")

            try:
                # 等待用户中断
                while hidden_manager.is_recording:
                    import time
                    time.sleep(1)
            except KeyboardInterrupt:
                print("正在停止录制...")
                hidden_manager.stop_recording()
                print("录制已停止")
        else:
            logger.error("启动隐藏录制失败")
            print("启动隐藏录制失败")

    except Exception as e:
        logger.error(f"隐藏录制过程中发生错误: {str(e)}")
        print(f"错误: {str(e)}")

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 显示版本信息
    if args.version:
        show_version()
        return

    # 以隐藏模式启动
    if args.hidden:
        start_hidden_recording(args.duration, args.output)
        return

    # 启动GUI
    app = QApplication(sys.argv)
    app.setApplicationName("功能强大的录屏软件")

    # 设置应用图标
    icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "recorder_icon.ico")
    app.setWindowIcon(QIcon(icon_path))

    # 创建主窗口
    window = MainWindow()
    window.show()

    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
