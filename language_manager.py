"""
语言管理模块 - 提供多语言支持
"""

import os
import json
import logging

import config

logger = logging.getLogger(__name__)

class LanguageManager:
    """语言管理器"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(LanguageManager, cls).__new__(cls)
            cls._instance.init()
        return cls._instance
    
    def init(self):
        """初始化"""
        self.current_language = config.GUI_CONFIG["language"]
        self.translations = {}
        self.supported_languages = ["zh_CN", "en_US"]
        
        # 加载所有支持的语言
        for lang in self.supported_languages:
            self._load_language(lang)
        
        logger.info(f"初始化语言管理器: 当前语言={self.current_language}")
    
    def _load_language(self, language_code):
        """
        加载语言文件
        
        参数:
            language_code (str): 语言代码
        
        返回:
            bool: 是否成功加载语言
        """
        try:
            # 如果语言文件不存在，则创建默认语言文件
            if language_code == "zh_CN":
                self.translations[language_code] = self._get_chinese_translations()
            elif language_code == "en_US":
                self.translations[language_code] = self._get_english_translations()
            else:
                logger.error(f"不支持的语言: {language_code}")
                return False
            
            logger.info(f"已加载语言: {language_code}")
            return True
        
        except Exception as e:
            logger.error(f"加载语言失败: {str(e)}")
            return False
    
    def _get_chinese_translations(self):
        """获取中文翻译"""
        return {
            # 通用
            "app_name": "功能强大的录屏软件",
            "ok": "确定",
            "cancel": "取消",
            "yes": "是",
            "no": "否",
            "warning": "警告",
            "error": "错误",
            "success": "成功",
            "info": "信息",
            
            # 主窗口
            "ready": "就绪",
            
            # 录制选项卡
            "record_tab": "录制",
            "record_mode": "录制模式",
            "full_screen": "全屏录制",
            "region": "区域录制",
            "record_settings": "录制设置",
            "fps": "帧率",
            "record_audio": "录制音频",
            "record_system_audio": "录制系统声音",
            "record_microphone": "录制麦克风",
            "output_dir": "输出目录",
            "browse": "浏览...",
            "start_recording": "开始录制",
            "pause_recording": "暂停录制",
            "resume_recording": "恢复录制",
            "stop_recording": "停止录制",
            "screenshot": "截图",
            "recording": "正在录制...",
            "recording_paused": "录制已暂停",
            "recording_stopped": "录制已停止",
            "recording_completed": "录制已完成",
            "recording_failed": "录制失败",
            "screenshot_saved": "截图已保存",
            "screenshot_failed": "截图失败",
            
            # 格式转换选项卡
            "convert_tab": "格式转换",
            "file_selection": "文件选择",
            "convert_settings": "转换设置",
            "target_format": "目标格式",
            "video_quality": "视频质量",
            "use_hardware_acceleration": "使用硬件加速",
            "start_conversion": "开始转换",
            "cancel_conversion": "取消转换",
            "converting": "正在转换...",
            "conversion_completed": "转换完成",
            "conversion_failed": "转换失败",
            "conversion_cancelled": "转换已取消",
            
            # 隐藏模式选项卡
            "hidden_tab": "隐藏模式",
            "hidden_mode_settings": "隐藏模式设置",
            "enable_hidden_mode": "启用隐藏模式",
            "start_hotkey": "开始录制快捷键",
            "stop_hotkey": "停止录制快捷键",
            "apply_hotkeys": "应用快捷键",
            "hide_from_taskbar": "从任务栏隐藏",
            "minimize_to_tray": "最小化到系统托盘",
            "timed_recording": "定时录制",
            "use_timed_recording": "使用定时录制",
            "minutes": "分钟",
            "start_hidden_recording": "开始隐藏录制",
            "stop_hidden_recording": "停止隐藏录制",
            "hidden_mode_status": "隐藏模式状态",
            "not_recording": "未录制",
            "hidden_recording_started": "隐藏录制已开始",
            "hidden_recording_stopped": "隐藏录制已停止",
            
            # 设置选项卡
            "settings_tab": "设置",
            "general_settings": "常规设置",
            "theme": "主题",
            "light_theme": "浅色",
            "dark_theme": "深色",
            "language": "语言",
            "minimize_on_close": "关闭时最小化到托盘",
            "always_on_top": "窗口总在最前",
            "output_settings": "输出设置",
            "default_output_dir": "默认输出目录",
            "apply_settings": "应用设置",
            "settings_applied": "设置已应用",
            
            # 关于
            "about": "关于",
            "version": "版本",
            "copyright": "版权所有",
            
            # 音视频合并
            "merge_audio_video": "合并音视频",
            "merge_audio_video_question": "是否将音频和视频合并为一个文件？",
            "merging": "正在合并音频和视频，请稍候...",
            "merge_completed": "音视频已合并",
            "merge_failed": "音视频合并失败",
            "no_merge": "您选择了不合并音视频",
            
            # 系统托盘
            "show_main_window": "显示主窗口",
            "hide_main_window": "隐藏主窗口",
            "exit": "退出",
            "minimized_to_tray": "应用程序已最小化到系统托盘"
        }
    
    def _get_english_translations(self):
        """获取英文翻译"""
        return {
            # General
            "app_name": "Powerful Screen Recorder",
            "ok": "OK",
            "cancel": "Cancel",
            "yes": "Yes",
            "no": "No",
            "warning": "Warning",
            "error": "Error",
            "success": "Success",
            "info": "Information",
            
            # Main Window
            "ready": "Ready",
            
            # Record Tab
            "record_tab": "Record",
            "record_mode": "Recording Mode",
            "full_screen": "Full Screen",
            "region": "Region",
            "record_settings": "Recording Settings",
            "fps": "FPS",
            "record_audio": "Record Audio",
            "record_system_audio": "Record System Audio",
            "record_microphone": "Record Microphone",
            "output_dir": "Output Directory",
            "browse": "Browse...",
            "start_recording": "Start Recording",
            "pause_recording": "Pause Recording",
            "resume_recording": "Resume Recording",
            "stop_recording": "Stop Recording",
            "screenshot": "Screenshot",
            "recording": "Recording...",
            "recording_paused": "Recording Paused",
            "recording_stopped": "Recording Stopped",
            "recording_completed": "Recording Completed",
            "recording_failed": "Recording Failed",
            "screenshot_saved": "Screenshot Saved",
            "screenshot_failed": "Screenshot Failed",
            
            # Convert Tab
            "convert_tab": "Convert",
            "file_selection": "File Selection",
            "convert_settings": "Conversion Settings",
            "target_format": "Target Format",
            "video_quality": "Video Quality",
            "use_hardware_acceleration": "Use Hardware Acceleration",
            "start_conversion": "Start Conversion",
            "cancel_conversion": "Cancel Conversion",
            "converting": "Converting...",
            "conversion_completed": "Conversion Completed",
            "conversion_failed": "Conversion Failed",
            "conversion_cancelled": "Conversion Cancelled",
            
            # Hidden Mode Tab
            "hidden_tab": "Hidden Mode",
            "hidden_mode_settings": "Hidden Mode Settings",
            "enable_hidden_mode": "Enable Hidden Mode",
            "start_hotkey": "Start Recording Hotkey",
            "stop_hotkey": "Stop Recording Hotkey",
            "apply_hotkeys": "Apply Hotkeys",
            "hide_from_taskbar": "Hide from Taskbar",
            "minimize_to_tray": "Minimize to System Tray",
            "timed_recording": "Timed Recording",
            "use_timed_recording": "Use Timed Recording",
            "minutes": "minutes",
            "start_hidden_recording": "Start Hidden Recording",
            "stop_hidden_recording": "Stop Hidden Recording",
            "hidden_mode_status": "Hidden Mode Status",
            "not_recording": "Not Recording",
            "hidden_recording_started": "Hidden Recording Started",
            "hidden_recording_stopped": "Hidden Recording Stopped",
            
            # Settings Tab
            "settings_tab": "Settings",
            "general_settings": "General Settings",
            "theme": "Theme",
            "light_theme": "Light",
            "dark_theme": "Dark",
            "language": "Language",
            "minimize_on_close": "Minimize to Tray on Close",
            "always_on_top": "Always on Top",
            "output_settings": "Output Settings",
            "default_output_dir": "Default Output Directory",
            "apply_settings": "Apply Settings",
            "settings_applied": "Settings Applied",
            
            # About
            "about": "About",
            "version": "Version",
            "copyright": "Copyright",
            
            # Audio Video Merge
            "merge_audio_video": "Merge Audio and Video",
            "merge_audio_video_question": "Do you want to merge audio and video into a single file?",
            "merging": "Merging audio and video, please wait...",
            "merge_completed": "Audio and video merged",
            "merge_failed": "Audio and video merge failed",
            "no_merge": "You chose not to merge audio and video",
            
            # System Tray
            "show_main_window": "Show Main Window",
            "hide_main_window": "Hide Main Window",
            "exit": "Exit",
            "minimized_to_tray": "Application minimized to system tray"
        }
    
    def get_translation(self, key, language_code=None):
        """
        获取翻译
        
        参数:
            key (str): 翻译键
            language_code (str): 语言代码，如果为None则使用当前语言
        
        返回:
            str: 翻译文本，如果未找到则返回键本身
        """
        if language_code is None:
            language_code = self.current_language
        
        if language_code not in self.translations:
            logger.warning(f"不支持的语言: {language_code}，使用默认语言")
            language_code = "zh_CN"
        
        return self.translations[language_code].get(key, key)
    
    def set_language(self, language_code):
        """
        设置当前语言
        
        参数:
            language_code (str): 语言代码
        
        返回:
            bool: 是否成功设置语言
        """
        if language_code not in self.supported_languages:
            logger.error(f"不支持的语言: {language_code}")
            return False
        
        self.current_language = language_code
        config.GUI_CONFIG["language"] = language_code
        
        logger.info(f"已设置语言: {language_code}")
        return True
    
    def get_current_language(self):
        """获取当前语言"""
        return self.current_language
    
    def get_supported_languages(self):
        """获取支持的语言列表"""
        return self.supported_languages
