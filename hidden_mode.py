"""
隐藏模式模块 - 负责实现隐藏式录制功能
"""

import os
import time
import threading
import logging
import keyboard
from datetime import datetime, timedelta

import config
import utils
from screen_recorder import ScreenRecorder
from audio_recorder import AudioRecorder

logger = logging.getLogger(__name__)

class HiddenModeManager:
    """隐藏模式管理器"""
    
    def __init__(self):
        """初始化隐藏模式管理器"""
        self.config = config.HIDDEN_MODE_CONFIG
        self.enabled = self.config["enabled"]
        self.hotkey_start = self.config["hotkey_start"]
        self.hotkey_stop = self.config["hotkey_stop"]
        
        # 录制器
        self.screen_recorder = None
        self.audio_recorder = None
        
        # 录制状态
        self.is_recording = False
        self.start_time = None
        self.scheduled_stop_time = None
        
        # 定时器线程
        self.timer_thread = None
        self.timer_running = False
        
        # 注册快捷键
        self._register_hotkeys()
        
        logger.info(f"初始化隐藏模式管理器: 启用={self.enabled}, "
                   f"开始快捷键={self.hotkey_start}, 停止快捷键={self.hotkey_stop}")
    
    def _register_hotkeys(self):
        """注册快捷键"""
        try:
            # 注册开始录制快捷键
            keyboard.add_hotkey(self.hotkey_start, self.start_recording)
            
            # 注册停止录制快捷键
            keyboard.add_hotkey(self.hotkey_stop, self.stop_recording)
            
            logger.info("已注册隐藏模式快捷键")
        
        except Exception as e:
            logger.error(f"注册快捷键失败: {str(e)}")
    
    def _unregister_hotkeys(self):
        """注销快捷键"""
        try:
            # 注销开始录制快捷键
            keyboard.remove_hotkey(self.hotkey_start)
            
            # 注销停止录制快捷键
            keyboard.remove_hotkey(self.hotkey_stop)
            
            logger.info("已注销隐藏模式快捷键")
        
        except Exception as e:
            logger.error(f"注销快捷键失败: {str(e)}")
    
    def enable(self):
        """启用隐藏模式"""
        if self.enabled:
            logger.warning("隐藏模式已经启用")
            return
        
        self.enabled = True
        self._register_hotkeys()
        logger.info("已启用隐藏模式")
    
    def disable(self):
        """禁用隐藏模式"""
        if not self.enabled:
            logger.warning("隐藏模式已经禁用")
            return
        
        # 如果正在录制，先停止录制
        if self.is_recording:
            self.stop_recording()
        
        self.enabled = False
        self._unregister_hotkeys()
        logger.info("已禁用隐藏模式")
    
    def start_recording(self, output_dir=None, duration_minutes=None):
        """
        开始隐藏录制
        
        参数:
            output_dir (str): 输出目录，如果为None则使用默认目录
            duration_minutes (int): 录制时长（分钟），如果为None则一直录制直到手动停止
        """
        if not self.enabled:
            logger.warning("隐藏模式未启用，无法开始录制")
            return False
        
        if self.is_recording:
            logger.warning("隐藏录制已经在进行中")
            return False
        
        try:
            # 设置输出目录
            if output_dir is None:
                output_dir = config.DEFAULT_OUTPUT_DIR
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            video_filename = f"hidden_recording_{timestamp}.mp4"
            audio_filename = f"hidden_recording_{timestamp}.wav"
            
            video_path = os.path.join(output_dir, video_filename)
            audio_path = os.path.join(output_dir, audio_filename)
            
            # 创建录制器
            self.screen_recorder = ScreenRecorder(output_path=video_path)
            self.audio_recorder = AudioRecorder(output_path=audio_path)
            
            # 开始录制
            self.screen_recorder.start_recording()
            self.audio_recorder.start_recording()
            
            # 设置录制状态
            self.is_recording = True
            self.start_time = datetime.now()
            
            # 如果设置了录制时长，启动定时器
            if duration_minutes is not None and duration_minutes > 0:
                self.scheduled_stop_time = self.start_time + timedelta(minutes=duration_minutes)
                self._start_timer(duration_minutes)
            else:
                self.scheduled_stop_time = None
            
            logger.info(f"开始隐藏录制: 视频={video_path}, 音频={audio_path}")
            
            if duration_minutes is not None and duration_minutes > 0:
                logger.info(f"计划录制时长: {duration_minutes}分钟, 预计结束时间: {self.scheduled_stop_time}")
            
            return True
        
        except Exception as e:
            logger.error(f"开始隐藏录制失败: {str(e)}")
            return False
    
    def stop_recording(self):
        """停止隐藏录制"""
        if not self.is_recording:
            logger.warning("没有正在进行的隐藏录制")
            return False
        
        try:
            # 停止定时器
            self.timer_running = False
            if self.timer_thread and self.timer_thread.is_alive():
                self.timer_thread.join(timeout=1.0)
            
            # 停止录制
            if self.screen_recorder:
                self.screen_recorder.stop_recording()
            
            if self.audio_recorder:
                self.audio_recorder.stop_recording()
            
            # 设置录制状态
            self.is_recording = False
            end_time = datetime.now()
            
            # 计算录制时长
            if self.start_time:
                duration = end_time - self.start_time
                duration_seconds = duration.total_seconds()
                duration_str = utils.format_time(duration_seconds)
                logger.info(f"停止隐藏录制: 录制时长={duration_str}")
            
            return True
        
        except Exception as e:
            logger.error(f"停止隐藏录制失败: {str(e)}")
            return False
    
    def _start_timer(self, duration_minutes):
        """启动定时器线程"""
        self.timer_running = True
        self.timer_thread = threading.Thread(
            target=self._timer_thread,
            args=(duration_minutes,)
        )
        self.timer_thread.daemon = True
        self.timer_thread.start()
    
    def _timer_thread(self, duration_minutes):
        """定时器线程函数"""
        try:
            # 计算结束时间
            end_time = time.time() + (duration_minutes * 60)
            
            # 等待直到结束时间或者定时器被取消
            while self.timer_running and time.time() < end_time:
                time.sleep(1.0)
            
            # 如果定时器未被取消，停止录制
            if self.timer_running and self.is_recording:
                logger.info("定时录制时间到，自动停止录制")
                self.stop_recording()
        
        except Exception as e:
            logger.error(f"定时器线程发生错误: {str(e)}")
    
    def set_hotkeys(self, hotkey_start=None, hotkey_stop=None):
        """
        设置快捷键
        
        参数:
            hotkey_start (str): 开始录制快捷键
            hotkey_stop (str): 停止录制快捷键
        
        返回:
            bool: 是否成功设置
        """
        try:
            # 注销旧快捷键
            self._unregister_hotkeys()
            
            # 设置新快捷键
            if hotkey_start:
                self.hotkey_start = hotkey_start
            
            if hotkey_stop:
                self.hotkey_stop = hotkey_stop
            
            # 注册新快捷键
            self._register_hotkeys()
            
            logger.info(f"已设置新快捷键: 开始={self.hotkey_start}, 停止={self.hotkey_stop}")
            return True
        
        except Exception as e:
            logger.error(f"设置快捷键失败: {str(e)}")
            return False
    
    def get_recording_status(self):
        """获取录制状态"""
        if not self.is_recording:
            return {
                "is_recording": False,
                "start_time": None,
                "duration": 0,
                "scheduled_stop_time": None
            }
        
        current_time = datetime.now()
        duration = (current_time - self.start_time).total_seconds()
        
        return {
            "is_recording": True,
            "start_time": self.start_time,
            "duration": duration,
            "scheduled_stop_time": self.scheduled_stop_time
        }
