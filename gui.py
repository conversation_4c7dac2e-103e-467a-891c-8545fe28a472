"""
图形用户界面模块 - 提供用户友好的界面
"""

import os
import sys
import time
import threading
import subprocess
import logging
import shutil
from datetime import datetime

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QComboBox, QSpinBox, QCheckBox, QTabWidget,
    QFileDialog, QMessageBox, QProgressBar, QSystemTrayIcon, QMenu,
    QAction, QSlider, QGroupBox, QRadioButton, QLineEdit, QFormLayout
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QSettings
from PyQt5.QtGui import QFont, QIcon

import config
import utils
from screen_recorder import ScreenRecorder
from audio_recorder import AudioRecorder
from format_converter import FormatConverter, ConversionStatus
from hidden_mode import HiddenModeManager
from status_display import StatusDisplay
from storage_manager import StorageManager
from settings_ui import SettingsUI

logger = logging.getLogger(__name__)

class RecordingThread(QThread):
    """录制线程"""
    update_timer = pyqtSignal(float)
    recording_stopped = pyqtSignal()

    def __init__(self, screen_recorder, audio_recorder):
        super().__init__()
        self.screen_recorder = screen_recorder
        self.audio_recorder = audio_recorder
        self.running = False

    def run(self):
        self.running = True

        while self.running:
            elapsed = self.screen_recorder.get_recording_time()
            self.update_timer.emit(elapsed)
            time.sleep(0.1)

        self.recording_stopped.emit()

class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()

        # 初始化录制器
        self.screen_recorder = None
        self.audio_recorder = None
        self.format_converter = FormatConverter()
        self.hidden_mode_manager = HiddenModeManager()

        # 初始化状态显示和存储管理器
        self.status_display = StatusDisplay(self)
        self.storage_manager = StorageManager(self)

        # 加载设置
        self.settings = QSettings("ScreenRecorder", "Settings")
        self.load_settings()

        # 录制状态
        self.is_recording = False
        self.is_paused = False
        self.recording_thread = None
        self.recording_start_time = None
        self.current_file_path = None

        # 设置窗口
        self.setWindowTitle("功能强大的录屏软件")
        self.setMinimumSize(800, 600)

        # 设置窗口图标
        icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "recorder_icon.ico")
        self.setWindowIcon(QIcon(icon_path))

        # 创建系统托盘图标
        self.setup_tray_icon()

        # 创建UI
        self.setup_ui()

        # 设置定时器更新状态
        self.status_timer = QTimer(self)
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新一次

        # 设置存储检查定时器
        self.storage_check_timer = QTimer(self)
        self.storage_check_timer.timeout.connect(self.check_recording_duration)
        self.storage_check_timer.start(60000)  # 每分钟检查一次

        # 连接存储管理器信号
        self.storage_manager.storage_warning.connect(self.show_storage_warning)
        self.storage_manager.cleanup_completed.connect(self.show_cleanup_notification)

        logger.info("GUI初始化完成")

    def setup_ui(self):
        """设置用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 创建选项卡
        tabs = QTabWidget()
        main_layout.addWidget(tabs)

        # 录制选项卡
        record_tab = QWidget()
        tabs.addTab(record_tab, "录制")
        self.setup_record_tab(record_tab)

        # 格式转换选项卡
        convert_tab = QWidget()
        tabs.addTab(convert_tab, "格式转换")
        self.setup_convert_tab(convert_tab)

        # 隐藏模式选项卡
        hidden_tab = QWidget()
        tabs.addTab(hidden_tab, "隐藏模式")
        self.setup_hidden_tab(hidden_tab)

        # 设置选项卡
        settings_tab = QWidget()
        tabs.addTab(settings_tab, "设置")
        self.setup_settings_tab(settings_tab)

        # 状态栏
        self.statusBar().showMessage("就绪")

    def setup_record_tab(self, tab):
        """设置录制选项卡"""
        layout = QVBoxLayout(tab)

        # 录制模式选择
        mode_group = QGroupBox("录制模式")
        mode_layout = QHBoxLayout()

        self.full_screen_radio = QRadioButton("全屏录制")
        self.full_screen_radio.setChecked(True)
        self.region_radio = QRadioButton("区域录制")

        mode_layout.addWidget(self.full_screen_radio)
        mode_layout.addWidget(self.region_radio)
        mode_group.setLayout(mode_layout)
        layout.addWidget(mode_group)

        # 录制设置
        settings_group = QGroupBox("录制设置")
        settings_layout = QFormLayout()

        # 帧率设置
        self.fps_combo = QComboBox()
        for fps in [15, 24, 30, 60]:
            self.fps_combo.addItem(f"{fps} FPS", fps)
        self.fps_combo.setCurrentText(f"{config.SCREEN_RECORD_CONFIG['fps']} FPS")
        settings_layout.addRow("帧率:", self.fps_combo)

        # 音频设置
        self.record_audio_check = QCheckBox("录制音频")
        self.record_audio_check.setChecked(config.SCREEN_RECORD_CONFIG["record_audio"])
        settings_layout.addRow("", self.record_audio_check)

        self.record_system_audio_check = QCheckBox("录制系统声音")
        self.record_system_audio_check.setChecked(config.SCREEN_RECORD_CONFIG["record_system_audio"])
        settings_layout.addRow("", self.record_system_audio_check)

        self.record_mic_check = QCheckBox("录制麦克风")
        self.record_mic_check.setChecked(config.SCREEN_RECORD_CONFIG["record_microphone"])
        settings_layout.addRow("", self.record_mic_check)

        # 输出设置
        self.output_path_edit = QLineEdit(config.DEFAULT_OUTPUT_DIR)
        self.output_path_edit.setReadOnly(True)

        browse_button = QPushButton("浏览...")
        browse_button.clicked.connect(self.browse_output_dir)

        output_layout = QHBoxLayout()
        output_layout.addWidget(self.output_path_edit)
        output_layout.addWidget(browse_button)

        settings_layout.addRow("输出目录:", output_layout)

        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)

        # 录制控制
        control_layout = QHBoxLayout()

        self.start_button = QPushButton("开始录制")
        self.start_button.clicked.connect(self.start_recording)

        self.pause_button = QPushButton("暂停录制")
        self.pause_button.clicked.connect(self.pause_recording)
        self.pause_button.setEnabled(False)

        self.stop_button = QPushButton("停止录制")
        self.stop_button.clicked.connect(self.stop_recording)
        self.stop_button.setEnabled(False)

        self.screenshot_button = QPushButton("截图")
        self.screenshot_button.clicked.connect(self.take_screenshot)

        control_layout.addWidget(self.start_button)
        control_layout.addWidget(self.pause_button)
        control_layout.addWidget(self.stop_button)
        control_layout.addWidget(self.screenshot_button)

        layout.addLayout(control_layout)

        # 录制状态
        status_layout = QHBoxLayout()

        self.time_label = QLabel("00:00:00")
        self.time_label.setAlignment(Qt.AlignCenter)
        self.time_label.setFont(QFont("Arial", 16, QFont.Bold))

        status_layout.addWidget(self.time_label)

        layout.addLayout(status_layout)

        # 添加状态显示组件
        layout.addWidget(self.status_display)

        # 填充空间
        layout.addStretch()

    def setup_convert_tab(self, tab):
        """设置格式转换选项卡"""
        layout = QVBoxLayout(tab)

        # 文件选择
        file_group = QGroupBox("文件选择")
        file_layout = QHBoxLayout()

        self.input_file_edit = QLineEdit()
        self.input_file_edit.setReadOnly(True)

        browse_input_button = QPushButton("浏览...")
        browse_input_button.clicked.connect(self.browse_input_file)

        file_layout.addWidget(self.input_file_edit)
        file_layout.addWidget(browse_input_button)

        file_group.setLayout(file_layout)
        layout.addWidget(file_group)

        # 转换设置
        convert_group = QGroupBox("转换设置")
        convert_layout = QFormLayout()

        # 目标格式
        self.format_combo = QComboBox()
        for fmt in self.format_converter.supported_formats:
            self.format_combo.addItem(fmt.upper(), fmt)
        convert_layout.addRow("目标格式:", self.format_combo)

        # 视频质量
        self.quality_slider = QSlider(Qt.Horizontal)
        self.quality_slider.setMinimum(0)
        self.quality_slider.setMaximum(100)
        self.quality_slider.setValue(80)
        self.quality_slider.setTickPosition(QSlider.TicksBelow)
        self.quality_slider.setTickInterval(10)

        self.quality_label = QLabel("80%")
        self.quality_slider.valueChanged.connect(
            lambda v: self.quality_label.setText(f"{v}%")
        )

        quality_layout = QHBoxLayout()
        quality_layout.addWidget(self.quality_slider)
        quality_layout.addWidget(self.quality_label)

        convert_layout.addRow("视频质量:", quality_layout)

        # 硬件加速
        self.hw_accel_check = QCheckBox("使用硬件加速")
        self.hw_accel_check.setChecked(config.FORMAT_CONVERSION_CONFIG["use_hardware_acceleration"])
        convert_layout.addRow("", self.hw_accel_check)

        convert_group.setLayout(convert_layout)
        layout.addWidget(convert_group)

        # 转换控制
        control_layout = QHBoxLayout()

        self.convert_button = QPushButton("开始转换")
        self.convert_button.clicked.connect(self.start_conversion)

        self.cancel_button = QPushButton("取消转换")
        self.cancel_button.clicked.connect(self.cancel_conversion)
        self.cancel_button.setEnabled(False)

        control_layout.addWidget(self.convert_button)
        control_layout.addWidget(self.cancel_button)

        layout.addLayout(control_layout)

        # 转换进度
        progress_layout = QVBoxLayout()

        self.convert_progress = QProgressBar()
        self.convert_progress.setRange(0, 100)
        self.convert_progress.setValue(0)

        self.convert_status = QLabel("就绪")

        progress_layout.addWidget(self.convert_progress)
        progress_layout.addWidget(self.convert_status)

        layout.addLayout(progress_layout)

        # 填充空间
        layout.addStretch()

    def setup_hidden_tab(self, tab):
        """设置隐藏模式选项卡"""
        layout = QVBoxLayout(tab)

        # 隐藏模式设置
        settings_group = QGroupBox("隐藏模式设置")
        settings_layout = QFormLayout()

        # 启用隐藏模式
        self.enable_hidden_check = QCheckBox("启用隐藏模式")
        self.enable_hidden_check.setChecked(self.hidden_mode_manager.enabled)
        self.enable_hidden_check.toggled.connect(self.toggle_hidden_mode)
        settings_layout.addRow("", self.enable_hidden_check)

        # 快捷键设置
        self.start_hotkey_edit = QLineEdit(self.hidden_mode_manager.hotkey_start)
        settings_layout.addRow("开始录制快捷键:", self.start_hotkey_edit)

        self.stop_hotkey_edit = QLineEdit(self.hidden_mode_manager.hotkey_stop)
        settings_layout.addRow("停止录制快捷键:", self.stop_hotkey_edit)

        # 应用快捷键按钮
        apply_hotkey_button = QPushButton("应用快捷键")
        apply_hotkey_button.clicked.connect(self.apply_hotkeys)
        settings_layout.addRow("", apply_hotkey_button)

        # 隐藏选项
        self.hide_taskbar_check = QCheckBox("从任务栏隐藏")
        self.hide_taskbar_check.setChecked(config.HIDDEN_MODE_CONFIG["hide_from_taskbar"])
        settings_layout.addRow("", self.hide_taskbar_check)

        self.minimize_tray_check = QCheckBox("最小化到系统托盘")
        self.minimize_tray_check.setChecked(config.HIDDEN_MODE_CONFIG["minimize_to_tray"])
        settings_layout.addRow("", self.minimize_tray_check)

        # 定时录制
        timer_layout = QHBoxLayout()

        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(1, 120)
        self.duration_spin.setValue(5)
        self.duration_spin.setSuffix(" 分钟")

        self.use_timer_check = QCheckBox("使用定时录制")

        timer_layout.addWidget(self.use_timer_check)
        timer_layout.addWidget(self.duration_spin)

        settings_layout.addRow("定时录制:", timer_layout)

        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)

        # 隐藏模式控制
        control_layout = QHBoxLayout()

        self.hidden_start_button = QPushButton("开始隐藏录制")
        self.hidden_start_button.clicked.connect(self.start_hidden_recording)

        self.hidden_stop_button = QPushButton("停止隐藏录制")
        self.hidden_stop_button.clicked.connect(self.stop_hidden_recording)
        self.hidden_stop_button.setEnabled(False)

        control_layout.addWidget(self.hidden_start_button)
        control_layout.addWidget(self.hidden_stop_button)

        layout.addLayout(control_layout)

        # 隐藏模式状态
        status_group = QGroupBox("隐藏模式状态")
        status_layout = QVBoxLayout()

        self.hidden_status_label = QLabel("未录制")
        self.hidden_time_label = QLabel("00:00:00")
        self.hidden_time_label.setAlignment(Qt.AlignCenter)
        self.hidden_time_label.setFont(QFont("Arial", 16, QFont.Bold))

        status_layout.addWidget(self.hidden_status_label)
        status_layout.addWidget(self.hidden_time_label)

        status_group.setLayout(status_layout)
        layout.addWidget(status_group)

        # 填充空间
        layout.addStretch()

    def setup_settings_tab(self, tab):
        """设置设置选项卡"""
        layout = QVBoxLayout(tab)

        # 创建设置UI组件
        self.settings_ui = SettingsUI(self)

        # 添加到布局
        layout.addWidget(self.settings_ui)

        # 保留原有的关于信息
        about_group = QGroupBox("关于")
        about_layout = QVBoxLayout()

        about_text = QLabel("功能强大的录屏软件 v1.0.0\n\n"
                           "一个支持隐藏式录屏和多通用格式转换的录屏软件。\n\n"
                           "© 2023 All Rights Reserved")
        about_text.setAlignment(Qt.AlignCenter)

        about_layout.addWidget(about_text)

        about_group.setLayout(about_layout)
        layout.addWidget(about_group)

        # 填充空间
        layout.addStretch()

    def setup_tray_icon(self):
        """设置系统托盘图标"""
        # 使用recorder_icon.ico作为托盘图标
        icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "recorder_icon.ico")
        icon = QIcon(icon_path)

        self.tray_icon = QSystemTrayIcon(self)
        self.tray_icon.setIcon(icon)
        self.tray_icon.setToolTip("功能强大的录屏软件")

        # 创建托盘菜单
        tray_menu = QMenu()

        show_action = QAction("显示主窗口", self)
        show_action.triggered.connect(self.show)

        hide_action = QAction("隐藏主窗口", self)
        hide_action.triggered.connect(self.hide)

        start_action = QAction("开始录制", self)
        start_action.triggered.connect(self.start_recording)

        stop_action = QAction("停止录制", self)
        stop_action.triggered.connect(self.stop_recording)

        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close_application)

        tray_menu.addAction(show_action)
        tray_menu.addAction(hide_action)
        tray_menu.addSeparator()
        tray_menu.addAction(start_action)
        tray_menu.addAction(stop_action)
        tray_menu.addSeparator()
        tray_menu.addAction(exit_action)

        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.show()

    def closeEvent(self, event):
        """窗口关闭事件"""
        if config.GUI_CONFIG["minimize_to_tray_on_close"]:
            event.ignore()
            self.hide()
            self.tray_icon.showMessage(
                "功能强大的录屏软件",
                "应用程序已最小化到系统托盘",
                QSystemTrayIcon.Information,
                2000
            )
        else:
            self.close_application()

    def close_application(self):
        """关闭应用程序"""
        # 停止录制
        if self.is_recording:
            self.stop_recording()

        # 停止隐藏录制
        if self.hidden_mode_manager.is_recording:
            self.hidden_mode_manager.stop_recording()

        # 取消转换
        if self.format_converter.status == ConversionStatus.CONVERTING:
            self.format_converter.cancel_conversion()

        # 关闭应用程序
        QApplication.quit()

    def update_status(self):
        """更新状态信息"""
        # 更新转换进度
        if self.format_converter.status == ConversionStatus.CONVERTING:
            progress = self.format_converter.get_conversion_progress()
            self.convert_progress.setValue(int(progress["progress"]))
            self.convert_status.setText("正在转换...")
            self.cancel_button.setEnabled(True)
            self.convert_button.setEnabled(False)
        elif self.format_converter.status == ConversionStatus.COMPLETED:
            self.convert_progress.setValue(100)
            self.convert_status.setText("转换完成")
            self.cancel_button.setEnabled(False)
            self.convert_button.setEnabled(True)
        elif self.format_converter.status == ConversionStatus.FAILED:
            self.convert_status.setText(f"转换失败: {self.format_converter.error_message}")
            self.cancel_button.setEnabled(False)
            self.convert_button.setEnabled(True)
        elif self.format_converter.status == ConversionStatus.CANCELLED:
            self.convert_status.setText("转换已取消")
            self.cancel_button.setEnabled(False)
            self.convert_button.setEnabled(True)

        # 更新隐藏模式状态
        if self.hidden_mode_manager.is_recording:
            status = self.hidden_mode_manager.get_recording_status()
            self.hidden_status_label.setText("正在录制")
            self.hidden_time_label.setText(utils.format_time(status["duration"]))
            self.hidden_start_button.setEnabled(False)
            self.hidden_stop_button.setEnabled(True)
        else:
            self.hidden_status_label.setText("未录制")
            self.hidden_time_label.setText("00:00:00")
            self.hidden_start_button.setEnabled(True)
            self.hidden_stop_button.setEnabled(False)

    def browse_output_dir(self):
        """浏览输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择输出目录", self.output_path_edit.text()
        )
        if dir_path:
            self.output_path_edit.setText(dir_path)

    def browse_default_output_dir(self):
        """浏览默认输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择默认输出目录", self.default_output_edit.text()
        )
        if dir_path:
            self.default_output_edit.setText(dir_path)

    def browse_input_file(self):
        """浏览输入文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择视频文件", "",
            "视频文件 (*.mp4 *.avi *.mkv *.wmv *.mov *.flv *.webm);;所有文件 (*)"
        )
        if file_path:
            self.input_file_edit.setText(file_path)

    def start_recording(self):
        """开始录制"""
        if self.is_recording:
            QMessageBox.warning(self, "警告", "录制已经在进行中")
            return

        try:
            # 获取录制设置
            fps = self.fps_combo.currentData()
            output_dir = self.output_path_edit.text()

            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            video_filename = f"recording_{timestamp}.mp4"
            audio_filename = f"recording_{timestamp}.wav"

            video_path = os.path.join(output_dir, video_filename)
            audio_path = os.path.join(output_dir, audio_filename)

            # 获取录制区域
            region = None
            if self.full_screen_radio.isChecked():
                region = None  # 全屏录制
            else:
                # 使用区域选择器
                from region_selector import select_screen_region
                region = select_screen_region(self)
                if region is None:
                    # 用户取消了区域选择
                    return

            # 创建录制器
            self.screen_recorder = ScreenRecorder(
                output_path=video_path,
                region=region,
                fps=fps
            )

            # 如果需要录制音频
            if self.record_audio_check.isChecked():
                self.audio_recorder = AudioRecorder(
                    output_path=audio_path,
                    record_system_audio=self.record_system_audio_check.isChecked(),
                    record_microphone=self.record_mic_check.isChecked()
                )
                self.audio_recorder.start_recording()

            # 开始录制
            self.screen_recorder.start_recording()

            # 设置录制状态
            self.is_recording = True
            self.is_paused = False
            self.recording_start_time = datetime.now()
            self.current_file_path = video_path

            # 更新状态显示
            self.status_display.start_recording(video_path)

            # 启动录制线程
            self.recording_thread = RecordingThread(self.screen_recorder, self.audio_recorder)
            self.recording_thread.update_timer.connect(self.update_recording_time)
            self.recording_thread.recording_stopped.connect(self.on_recording_stopped)
            self.recording_thread.start()

            # 更新UI
            self.start_button.setEnabled(False)
            self.pause_button.setEnabled(True)
            self.stop_button.setEnabled(True)
            self.statusBar().showMessage("正在录制...")

            logger.info(f"开始录制: 视频={video_path}, 音频={audio_path if self.record_audio_check.isChecked() else '无'}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"开始录制失败: {str(e)}")
            logger.error(f"开始录制失败: {str(e)}")

    def pause_recording(self):
        """暂停/恢复录制"""
        if not self.is_recording:
            return

        try:
            # 暂停/恢复屏幕录制
            self.screen_recorder.pause_recording()

            # 暂停/恢复音频录制
            if self.record_audio_check.isChecked() and self.audio_recorder:
                self.audio_recorder.pause_recording()

            # 更新状态
            self.is_paused = not self.is_paused

            # 更新UI
            if self.is_paused:
                self.pause_button.setText("恢复录制")
                self.statusBar().showMessage("录制已暂停")
                logger.info("录制已暂停")
            else:
                self.pause_button.setText("暂停录制")
                self.statusBar().showMessage("正在录制...")
                logger.info("录制已恢复")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"暂停/恢复录制失败: {str(e)}")
            logger.error(f"暂停/恢复录制失败: {str(e)}")

    def stop_recording(self):
        """停止录制"""
        if not self.is_recording:
            return

        try:
            # 停止录制线程
            if self.recording_thread and self.recording_thread.isRunning():
                self.recording_thread.running = False
                self.recording_thread.wait()

            # 停止屏幕录制
            video_path = self.screen_recorder.output_path
            self.screen_recorder.stop_recording()

            # 停止音频录制
            audio_path = None
            if self.record_audio_check.isChecked() and self.audio_recorder:
                audio_path = self.audio_recorder.output_path
                self.audio_recorder.stop_recording()

            # 更新状态
            self.is_recording = False
            self.is_paused = False
            self.recording_start_time = None
            self.current_file_path = None

            # 更新状态显示
            self.status_display.stop_recording()

            # 更新UI
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.pause_button.setText("暂停录制")
            self.stop_button.setEnabled(False)
            self.time_label.setText("00:00:00")
            self.statusBar().showMessage("录制已停止")

            logger.info(f"停止录制: 视频={video_path}, 音频={audio_path if audio_path else '无'}")

            # 询问是否合并音视频
            if audio_path and os.path.exists(audio_path) and os.path.exists(video_path):
                # 使用悬浮通知询问是否合并
                from notification import NotificationManager, NotificationType

                # 先显示录制完成通知
                NotificationManager().show_notification(
                    f"录制已完成并保存到: {video_path}",
                    notification_type=NotificationType.SUCCESS,
                    duration=5000,
                    parent=self
                )

                # 然后询问是否合并
                reply = QMessageBox.question(
                    self, "合并音视频",
                    "是否将音频和视频合并为一个文件？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    merged_path = self._merge_audio_video(video_path, audio_path)
                    if merged_path:
                        video_path = merged_path  # 更新视频路径为合并后的文件
                    # 合并结果通知已在_merge_audio_video方法中处理
                else:
                    # 用户选择不合并，显示提示
                    NotificationManager().show_notification(
                        "您选择了不合并音视频",
                        notification_type=NotificationType.INFO,
                        duration=3000,
                        parent=self
                    )
            else:
                # 没有音频文件，直接显示录制完成通知
                from notification import NotificationManager, NotificationType
                NotificationManager().show_notification(
                    f"录制已完成并保存到: {video_path}",
                    notification_type=NotificationType.SUCCESS,
                    duration=5000,
                    parent=self
                )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止录制失败: {str(e)}")
            logger.error(f"停止录制失败: {str(e)}")

    def update_recording_time(self, elapsed_time):
        """更新录制时间显示"""
        self.time_label.setText(utils.format_time(elapsed_time))

    def on_recording_stopped(self):
        """录制停止时的回调"""
        self.stop_recording()

    def take_screenshot(self):
        """截图"""
        try:
            # 创建临时屏幕录制器
            output_path = utils.get_output_file_path(prefix="screenshot", extension="png")

            # 获取录制区域
            region = None
            if self.full_screen_radio.isChecked():
                region = None  # 全屏截图
            else:
                # 使用区域选择器
                from region_selector import select_screen_region
                region = select_screen_region(self)
                if region is None:
                    # 用户取消了区域选择
                    return

            # 创建屏幕录制器并截图
            screen_recorder = ScreenRecorder(region=region)
            screenshot_path = screen_recorder.take_screenshot(output_path)

            if screenshot_path:
                self.statusBar().showMessage(f"截图已保存: {screenshot_path}")
                # 使用悬浮通知替代弹窗
                from notification import NotificationManager, NotificationType
                NotificationManager().show_notification(
                    f"截图已保存到: {screenshot_path}",
                    notification_type=NotificationType.SUCCESS,
                    duration=5000,
                    parent=self
                )
            else:
                from notification import NotificationManager, NotificationType
                NotificationManager().show_notification(
                    "截图保存失败",
                    notification_type=NotificationType.ERROR,
                    duration=5000,
                    parent=self
                )

        except Exception as e:
            from notification import NotificationManager, NotificationType
            NotificationManager().show_notification(
                f"截图失败: {str(e)}",
                notification_type=NotificationType.ERROR,
                duration=5000,
                parent=self
            )
            logger.error(f"截图失败: {str(e)}")

    def start_conversion(self):
        """开始格式转换"""
        input_path = self.input_file_edit.text()

        if not input_path or not os.path.exists(input_path):
            QMessageBox.warning(self, "警告", "请选择有效的输入文件")
            return

        try:
            # 获取转换设置
            target_format = self.format_combo.currentData()
            quality = self.quality_slider.value()
            use_hw_accel = self.hw_accel_check.isChecked()

            # 设置输出路径
            input_dir = os.path.dirname(input_path)
            input_name = os.path.splitext(os.path.basename(input_path))[0]
            output_path = os.path.join(input_dir, f"{input_name}_converted.{target_format}")

            # 更新硬件加速设置
            self.format_converter.use_hardware_acceleration = use_hw_accel

            # 开始转换
            success = self.format_converter.convert_video(
                input_path=input_path,
                output_path=output_path,
                target_format=target_format,
                quality=quality
            )

            if success:
                self.convert_status.setText("正在转换...")
                self.convert_button.setEnabled(False)
                self.cancel_button.setEnabled(True)
                self.statusBar().showMessage("正在转换视频...")
            else:
                QMessageBox.warning(self, "警告", "开始转换失败")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"开始转换失败: {str(e)}")
            logger.error(f"开始转换失败: {str(e)}")

    def cancel_conversion(self):
        """取消格式转换"""
        try:
            if self.format_converter.cancel_conversion():
                self.convert_status.setText("转换已取消")
                self.convert_button.setEnabled(True)
                self.cancel_button.setEnabled(False)
                self.statusBar().showMessage("转换已取消")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"取消转换失败: {str(e)}")
            logger.error(f"取消转换失败: {str(e)}")

    def toggle_hidden_mode(self, enabled):
        """切换隐藏模式"""
        try:
            if enabled:
                self.hidden_mode_manager.enable()
            else:
                self.hidden_mode_manager.disable()

            # 更新UI
            self.hidden_start_button.setEnabled(enabled)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"切换隐藏模式失败: {str(e)}")
            logger.error(f"切换隐藏模式失败: {str(e)}")

    def apply_hotkeys(self):
        """应用快捷键设置"""
        try:
            hotkey_start = self.start_hotkey_edit.text()
            hotkey_stop = self.stop_hotkey_edit.text()

            if not hotkey_start or not hotkey_stop:
                QMessageBox.warning(self, "警告", "快捷键不能为空")
                return

            if self.hidden_mode_manager.set_hotkeys(hotkey_start, hotkey_stop):
                QMessageBox.information(self, "成功", "快捷键设置已应用")
            else:
                QMessageBox.warning(self, "警告", "应用快捷键设置失败")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用快捷键设置失败: {str(e)}")
            logger.error(f"应用快捷键设置失败: {str(e)}")

    def start_hidden_recording(self):
        """开始隐藏录制"""
        try:
            # 获取设置
            output_dir = self.output_path_edit.text()
            duration_minutes = None

            if self.use_timer_check.isChecked():
                duration_minutes = self.duration_spin.value()

            # 开始隐藏录制
            if self.hidden_mode_manager.start_recording(output_dir, duration_minutes):
                self.hidden_start_button.setEnabled(False)
                self.hidden_stop_button.setEnabled(True)
                self.statusBar().showMessage("隐藏录制已开始")

                # 如果设置了最小化到托盘
                if self.minimize_tray_check.isChecked():
                    self.hide()
                    self.tray_icon.showMessage(
                        "隐藏录制",
                        "隐藏录制已开始",
                        QSystemTrayIcon.Information,
                        2000
                    )
            else:
                QMessageBox.warning(self, "警告", "开始隐藏录制失败")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"开始隐藏录制失败: {str(e)}")
            logger.error(f"开始隐藏录制失败: {str(e)}")

    def stop_hidden_recording(self):
        """停止隐藏录制"""
        try:
            if self.hidden_mode_manager.stop_recording():
                self.hidden_start_button.setEnabled(True)
                self.hidden_stop_button.setEnabled(False)
                self.statusBar().showMessage("隐藏录制已停止")

                # 如果窗口被隐藏，则显示窗口
                if not self.isVisible():
                    self.show()

                # 使用悬浮通知替代弹窗
                from notification import NotificationManager, NotificationType
                NotificationManager().show_notification(
                    "隐藏录制已停止",
                    notification_type=NotificationType.INFO,
                    duration=3000,
                    parent=self
                )
            else:
                QMessageBox.warning(self, "警告", "停止隐藏录制失败")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止隐藏录制失败: {str(e)}")
            logger.error(f"停止隐藏录制失败: {str(e)}")

    def toggle_always_on_top(self, checked):
        """切换窗口总在最前"""
        if checked:
            self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        else:
            self.setWindowFlags(self.windowFlags() & ~Qt.WindowStaysOnTopHint)
        self.show()

    def _merge_audio_video(self, video_path, audio_path):
        """
        合并音频和视频文件

        参数:
            video_path (str): 视频文件路径
            audio_path (str): 音频文件路径

        返回:
            str: 合并后的文件路径，如果失败则返回None
        """
        from notification import NotificationManager, NotificationType

        try:
            # 检查文件是否存在
            if not os.path.exists(video_path):
                error_msg = f"视频文件不存在: {video_path}"
                logger.error(error_msg)
                NotificationManager().show_notification(
                    error_msg,
                    notification_type=NotificationType.ERROR,
                    duration=5000,
                    parent=self
                )
                return None

            if not os.path.exists(audio_path):
                error_msg = f"音频文件不存在: {audio_path}"
                logger.error(error_msg)
                NotificationManager().show_notification(
                    error_msg,
                    notification_type=NotificationType.ERROR,
                    duration=5000,
                    parent=self
                )
                return None

            # 检查文件大小
            video_size = os.path.getsize(video_path)
            audio_size = os.path.getsize(audio_path)

            if video_size == 0:
                error_msg = f"视频文件为空: {video_path}"
                logger.error(error_msg)
                NotificationManager().show_notification(
                    error_msg,
                    notification_type=NotificationType.ERROR,
                    duration=5000,
                    parent=self
                )
                return None

            if audio_size == 0:
                error_msg = f"音频文件为空: {audio_path}"
                logger.error(error_msg)
                NotificationManager().show_notification(
                    error_msg,
                    notification_type=NotificationType.ERROR,
                    duration=5000,
                    parent=self
                )
                return None

            # 获取视频和音频的时长
            video_duration = utils.get_media_duration(video_path)
            audio_duration = utils.get_media_duration(audio_path)

            if video_duration is not None and audio_duration is not None:
                logger.info(f"视频时长: {video_duration:.2f}秒, 音频时长: {audio_duration:.2f}秒")

                # 如果视频时长明显短于音频时长，记录警告
                if video_duration < audio_duration * 0.9:  # 视频时长小于音频时长的90%
                    logger.warning(f"视频时长({video_duration:.2f}秒)明显短于音频时长({audio_duration:.2f}秒)，可能会导致合并后的文件时长不正确")
                elif audio_duration < video_duration * 0.9:  # 音频时长小于视频时长的90%
                    logger.warning(f"音频时长({audio_duration:.2f}秒)明显短于视频时长({video_duration:.2f}秒)，可能会导致合并后的文件时长不正确")

            # 检查FFmpeg是否可用
            if not utils.check_ffmpeg_installed():
                error_msg = "FFmpeg未安装或路径不正确，无法合并音视频"
                logger.error(error_msg)

                # 询问用户是否要下载FFmpeg
                reply = QMessageBox.question(
                    self, "FFmpeg未安装",
                    "FFmpeg未安装或路径不正确，无法合并音视频。\n是否打开FFmpeg下载页面并查看安装指南？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if reply == QMessageBox.Yes:
                    # 打开FFmpeg下载页面
                    utils.download_ffmpeg()

                    # 显示通知
                    NotificationManager().show_notification(
                        "请按照安装指南安装FFmpeg，然后重试合并操作",
                        notification_type=NotificationType.INFO,
                        duration=8000,
                        parent=self
                    )
                else:
                    # 显示错误通知
                    NotificationManager().show_notification(
                        error_msg,
                        notification_type=NotificationType.ERROR,
                        duration=5000,
                        parent=self
                    )
                return None

            # 生成输出文件路径
            output_dir = os.path.dirname(video_path)
            video_name = os.path.splitext(os.path.basename(video_path))[0]
            output_path = os.path.join(output_dir, f"{video_name}_合成完毕.mp4")

            # 构建FFmpeg命令
            ffmpeg_path = config.FORMAT_CONVERSION_CONFIG["ffmpeg_path"]

            # 先检查视频和音频文件的格式
            try:
                # 检查视频文件
                video_info_cmd = [
                    ffmpeg_path,
                    "-i", video_path,
                    "-hide_banner"
                ]
                video_info = subprocess.run(
                    video_info_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )
                logger.debug(f"视频文件信息: {video_info.stderr}")

                # 检查音频文件
                audio_info_cmd = [
                    ffmpeg_path,
                    "-i", audio_path,
                    "-hide_banner"
                ]
                audio_info = subprocess.run(
                    audio_info_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )
                logger.debug(f"音频文件信息: {audio_info.stderr}")
            except Exception as e:
                logger.warning(f"获取媒体文件信息失败: {str(e)}")
                # 继续执行，不中断流程

            # 构建合并命令 - 解决时长不匹配问题
            command = [
                ffmpeg_path,
                "-i", video_path,  # 视频输入
                "-i", audio_path,  # 音频输入
                "-c:v", "libx264", # 使用H.264编码视频
                "-preset", "medium", # 编码预设
                "-crf", "23",      # 视频质量
                "-c:a", "aac",     # 使用AAC编码音频
                "-b:a", "192k",    # 设置音频比特率
                "-ac", "2",        # 设置音频通道数
                "-ar", "44100",    # 设置音频采样率
                "-strict", "experimental",
                # 强制使用视频的原始帧率
                "-vsync", "0",     # 禁用帧率转换
                # 添加音频同步选项
                "-async", "1",     # 音频同步
                # 使用音频时长作为主时长
                "-shortest",       # 以较短的流结束
                # 添加视频帧率控制，确保视频帧率稳定
                "-r", "30",        # 设置输出视频帧率为30fps
                "-y",              # 覆盖输出文件
                output_path
            ]

            # 显示悬浮通知
            notification = NotificationManager().show_notification(
                "正在合并音频和视频，请稍候...",
                notification_type=NotificationType.PROGRESS,
                duration=0,  # 不自动关闭
                closable=True,
                position="bottom_right",
                parent=self
            )

            # 执行FFmpeg命令
            logger.info(f"开始合并音视频: {video_path} + {audio_path} -> {output_path}")
            logger.debug(f"FFmpeg命令: {' '.join(command)}")

            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )

            # 等待进程完成
            stdout, stderr = process.communicate()

            # 记录详细的输出信息
            logger.debug(f"FFmpeg标准输出: {stdout}")
            logger.debug(f"FFmpeg错误输出: {stderr}")

            # 关闭通知
            if notification:
                notification.close_animation()

            # 验证输出文件是否存在且有效
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                logger.info(f"合并后的文件已创建: {output_path}, 大小: {os.path.getsize(output_path)} 字节")
            else:
                logger.error(f"合并后的文件不存在或为空: {output_path}")
                if os.path.exists(output_path):
                    logger.error(f"文件大小为: {os.path.getsize(output_path)} 字节")

            # 检查进程返回码
            if process.returncode == 0:
                logger.info(f"音视频合并完成: {output_path}")

                # 检查合并后文件的时长
                merged_duration = utils.get_media_duration(output_path)
                if merged_duration is not None:
                    logger.info(f"合并后文件时长: {merged_duration:.2f}秒")

                    # 如果有原始时长信息，进行比较
                    video_duration = utils.get_media_duration(video_path)
                    audio_duration = utils.get_media_duration(audio_path)

                    if video_duration is not None and audio_duration is not None:
                        # 使用较长的时长作为参考
                        reference_duration = max(video_duration, audio_duration)

                        # 如果合并后的时长明显短于参考时长，记录警告
                        if merged_duration < reference_duration * 0.9:
                            logger.warning(f"合并后的文件时长({merged_duration:.2f}秒)明显短于原始媒体时长({reference_duration:.2f}秒)")
                            # 如果时长差异太大，尝试使用备用方法
                            if merged_duration < reference_duration * 0.5:
                                logger.warning("时长差异过大，尝试使用备用方法")
                                return self._merge_audio_video_alternative(video_path, audio_path)

                # 显示成功通知
                NotificationManager().show_notification(
                    f"音视频已合并并保存到: {output_path}",
                    notification_type=NotificationType.SUCCESS,
                    duration=5000,
                    parent=self
                )
                return output_path
            else:
                # 记录详细错误信息
                logger.error(f"音视频合并失败，返回码: {process.returncode}")
                logger.error(f"错误输出: {stderr}")

                # 尝试使用备用方法合并
                return self._merge_audio_video_alternative(video_path, audio_path)

        except Exception as e:
            logger.error(f"音视频合并过程中发生错误: {str(e)}")
            # 显示错误通知
            NotificationManager().show_notification(
                f"音视频合并过程中发生错误: {str(e)}",
                notification_type=NotificationType.ERROR,
                duration=5000,
                parent=self
            )
            return None

    def _merge_audio_video_alternative(self, video_path, audio_path):
        """
        使用备用方法合并音频和视频文件

        参数:
            video_path (str): 视频文件路径
            audio_path (str): 音频文件路径

        返回:
            str: 合并后的文件路径，如果失败则返回None
        """
        from notification import NotificationManager, NotificationType

        try:
            # 生成输出文件路径
            output_dir = os.path.dirname(video_path)
            video_name = os.path.splitext(os.path.basename(video_path))[0]
            output_path = os.path.join(output_dir, f"{video_name}_备用方法合成完毕.mp4")

            # 构建备用FFmpeg命令 - 使用完全不同的方法
            ffmpeg_path = config.FORMAT_CONVERSION_CONFIG["ffmpeg_path"]

            # 方法2：使用不同的方法解决时长不匹配问题
            command = [
                ffmpeg_path,
                "-i", video_path,
                "-i", audio_path,
                # 使用视频时长作为参考
                "-c:v", "libx264",  # 使用H.264编码视频
                "-c:a", "aac",      # 使用AAC编码音频
                "-b:a", "192k",     # 设置音频比特率
                "-ac", "2",         # 设置音频通道数
                "-ar", "44100",     # 设置音频采样率
                "-strict", "experimental",
                # 设置视频帧率
                "-r", "30",         # 设置输出视频帧率为30fps
                # 添加音频同步选项
                "-async", "1",      # 音频同步
                # 添加视频时长控制
                "-t", "9999",       # 设置一个很大的时长值，确保不会截断
                "-y",               # 覆盖输出文件
                output_path
            ]

            # 显示悬浮通知
            notification = NotificationManager().show_notification(
                "正在尝试备用方法合并音视频，请稍候...",
                notification_type=NotificationType.PROGRESS,
                duration=0,  # 不自动关闭
                closable=True,
                position="bottom_right",
                parent=self
            )

            # 执行FFmpeg命令
            logger.info(f"使用备用方法合并音视频: {video_path} + {audio_path} -> {output_path}")
            logger.debug(f"备用FFmpeg命令: {' '.join(command)}")

            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )

            # 等待进程完成
            stdout, stderr = process.communicate()

            # 记录详细的输出信息
            logger.debug(f"备用方法FFmpeg标准输出: {stdout}")
            logger.debug(f"备用方法FFmpeg错误输出: {stderr}")

            # 关闭通知
            if notification:
                notification.close_animation()

            # 验证输出文件是否存在且有效
            if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                logger.info(f"备用方法合并后的文件已创建: {output_path}, 大小: {os.path.getsize(output_path)} 字节")
            else:
                logger.error(f"备用方法合并后的文件不存在或为空: {output_path}")
                if os.path.exists(output_path):
                    logger.error(f"文件大小为: {os.path.getsize(output_path)} 字节")

            # 检查进程返回码
            if process.returncode == 0:
                logger.info(f"备用方法音视频合并完成: {output_path}")

                # 检查合并后文件的时长
                merged_duration = utils.get_media_duration(output_path)
                if merged_duration is not None:
                    logger.info(f"备用方法合并后文件时长: {merged_duration:.2f}秒")

                    # 如果有原始时长信息，进行比较
                    video_duration = utils.get_media_duration(video_path)
                    audio_duration = utils.get_media_duration(audio_path)

                    if video_duration is not None and audio_duration is not None:
                        # 使用较长的时长作为参考
                        reference_duration = max(video_duration, audio_duration)

                        # 如果合并后的时长明显短于参考时长，记录警告
                        if merged_duration < reference_duration * 0.9:
                            logger.warning(f"备用方法合并后的文件时长({merged_duration:.2f}秒)明显短于原始媒体时长({reference_duration:.2f}秒)")
                            # 如果时长差异太大，尝试使用第三种方法
                            if merged_duration < reference_duration * 0.5:
                                logger.warning("时长差异过大，尝试使用最终方法")
                                return self._merge_audio_video_alternative2(video_path, audio_path)

                # 显示成功通知
                NotificationManager().show_notification(
                    f"音视频已合并并保存到: {output_path}",
                    notification_type=NotificationType.SUCCESS,
                    duration=5000,
                    parent=self
                )
                return output_path
            else:
                # 记录详细错误信息
                logger.error(f"备用方法音视频合并失败，返回码: {process.returncode}")
                logger.error(f"错误输出: {stderr}")

                # 尝试第三种方法
                return self._merge_audio_video_alternative2(video_path, audio_path)

        except Exception as e:
            logger.error(f"备用方法音视频合并过程中发生错误: {str(e)}")
            # 显示错误通知
            NotificationManager().show_notification(
                f"备用方法音视频合并过程中发生错误: {str(e)}",
                notification_type=NotificationType.ERROR,
                duration=5000,
                parent=self
            )
            return None

    def _merge_audio_video_alternative2(self, video_path, audio_path):
        """
        使用第三种备用方法合并音频和视频文件

        参数:
            video_path (str): 视频文件路径
            audio_path (str): 音频文件路径

        返回:
            str: 合并后的文件路径，如果失败则返回None
        """
        from notification import NotificationManager, NotificationType

        try:
            # 生成输出文件路径
            output_dir = os.path.dirname(video_path)
            video_name = os.path.splitext(os.path.basename(video_path))[0]
            output_path = os.path.join(output_dir, f"{video_name}_最终方法合成完毕.mp4")

            # 构建第三种备用FFmpeg命令 - 使用完全不同的方法
            ffmpeg_path = config.FORMAT_CONVERSION_CONFIG["ffmpeg_path"]

            # 方法3：先将音频转换为相同格式，然后合并
            temp_audio_path = os.path.join(output_dir, f"{video_name}_temp_audio.aac")

            # 第一步：转换音频为AAC格式，使用更可靠的参数
            audio_convert_cmd = [
                ffmpeg_path,
                "-i", audio_path,
                "-c:a", "aac",
                "-b:a", "192k",
                "-ac", "2",        # 设置音频通道数
                "-ar", "44100",    # 设置音频采样率
                "-y",
                temp_audio_path
            ]

            # 显示悬浮通知
            notification = NotificationManager().show_notification(
                "正在尝试最终方法合并音视频，请稍候...",
                notification_type=NotificationType.PROGRESS,
                duration=0,  # 不自动关闭
                closable=True,
                position="bottom_right",
                parent=self
            )

            # 执行音频转换命令
            logger.info(f"转换音频格式: {audio_path} -> {temp_audio_path}")
            audio_process = subprocess.run(
                audio_convert_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )

            if audio_process.returncode != 0:
                logger.error(f"音频转换失败: {audio_process.stderr}")
                if notification:
                    notification.close_animation()

                # 显示错误通知
                NotificationManager().show_notification(
                    "音视频合并失败，请检查日志获取详细信息",
                    notification_type=NotificationType.ERROR,
                    duration=5000,
                    parent=self
                )
                return None

            # 第二步：合并视频和转换后的音频，解决时长不匹配问题
            merge_cmd = [
                ffmpeg_path,
                "-i", video_path,
                "-i", temp_audio_path,
                # 重新编码视频而不是简单复制，以确保帧率稳定
                "-c:v", "libx264",
                "-preset", "medium",
                "-crf", "23",
                # 复制已经转换好的音频
                "-c:a", "copy",
                # 设置视频帧率
                "-r", "30",
                # 添加音频同步选项
                "-async", "1",
                # 确保使用完整的音频时长
                "-max_muxing_queue_size", "1024",
                "-y",
                output_path
            ]

            # 执行合并命令
            logger.info(f"最终方法合并音视频: {video_path} + {temp_audio_path} -> {output_path}")
            merge_process = subprocess.run(
                merge_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )

            # 关闭通知
            if notification:
                notification.close_animation()

            # 删除临时文件
            try:
                if os.path.exists(temp_audio_path):
                    os.remove(temp_audio_path)
            except Exception as e:
                logger.warning(f"删除临时文件失败: {str(e)}")

            # 检查进程返回码
            if merge_process.returncode == 0:
                logger.info(f"最终方法音视频合并完成: {output_path}")

                # 检查合并后文件的时长
                merged_duration = utils.get_media_duration(output_path)
                if merged_duration is not None:
                    logger.info(f"最终方法合并后文件时长: {merged_duration:.2f}秒")

                    # 如果有原始时长信息，进行比较
                    video_duration = utils.get_media_duration(video_path)
                    audio_duration = utils.get_media_duration(audio_path)

                    if video_duration is not None and audio_duration is not None:
                        # 使用较长的时长作为参考
                        reference_duration = max(video_duration, audio_duration)

                        # 如果合并后的时长明显短于参考时长，记录警告
                        if merged_duration < reference_duration * 0.9:
                            logger.warning(f"最终方法合并后的文件时长({merged_duration:.2f}秒)明显短于原始媒体时长({reference_duration:.2f}秒)")

                # 显示成功通知
                NotificationManager().show_notification(
                    f"音视频已合并并保存到: {output_path}",
                    notification_type=NotificationType.SUCCESS,
                    duration=5000,
                    parent=self
                )
                return output_path
            else:
                # 记录详细错误信息
                logger.error(f"最终方法音视频合并失败，返回码: {merge_process.returncode}")
                logger.error(f"错误输出: {merge_process.stderr}")

                # 显示错误通知
                NotificationManager().show_notification(
                    "所有合并方法都失败，请检查日志获取详细信息",
                    notification_type=NotificationType.ERROR,
                    duration=5000,
                    parent=self
                )
                return None

        except Exception as e:
            logger.error(f"最终方法音视频合并过程中发生错误: {str(e)}")
            # 显示错误通知
            NotificationManager().show_notification(
                f"最终方法音视频合并过程中发生错误: {str(e)}",
                notification_type=NotificationType.ERROR,
                duration=5000,
                parent=self
            )
            return None

    def load_settings(self):
        """从QSettings加载设置"""
        try:
            # 加载存储管理器设置
            max_storage_time = self.settings.value("max_storage_time", 24, type=int)
            max_folder_size = self.settings.value("max_folder_size", 30, type=int)
            disk_limit = self.settings.value("disk_limit", 5, type=int)

            # 更新存储管理器
            self.storage_manager.update_settings(
                max_time=max_storage_time,
                max_size=max_folder_size,
                disk_limit=disk_limit
            )

            # 加载单视频文件时长限制
            self.max_video_duration = self.settings.value("duration", 30, type=int)

            logger.info(f"已加载设置: 最大存储时间={max_storage_time}小时, "
                       f"最大文件夹大小={max_folder_size}GB, 硬盘警戒线={disk_limit}GB, "
                       f"单视频时长={self.max_video_duration}分钟")

        except Exception as e:
            logger.error(f"加载设置时发生错误: {str(e)}")
            # 使用默认值
            self.max_video_duration = 30

    def check_recording_duration(self):
        """检查录制时长，如果超过限制则分割文件"""
        if not self.is_recording or self.is_paused:
            return

        if not self.recording_start_time or not self.current_file_path:
            return

        # 计算当前录制时长（分钟）
        elapsed_minutes = (datetime.now() - self.recording_start_time).total_seconds() / 60

        # 如果超过单文件时长限制，停止当前录制并开始新的录制
        if elapsed_minutes >= self.max_video_duration:
            logger.info(f"录制时长({elapsed_minutes:.2f}分钟)已超过限制({self.max_video_duration}分钟)，分割文件")

            # 保存当前设置
            current_settings = {
                "full_screen": self.full_screen_radio.isChecked(),
                "fps": self.fps_combo.currentData(),
                "record_audio": self.record_audio_check.isChecked(),
                "record_system_audio": self.record_system_audio_check.isChecked(),
                "record_mic": self.record_mic_check.isChecked()
            }

            # 停止当前录制
            self.stop_recording()

            # 短暂延迟，确保文件已保存
            QTimer.singleShot(1000, lambda: self.start_new_recording(current_settings))

    def start_new_recording(self, settings):
        """开始新的录制"""
        try:
            # 应用之前的设置
            self.full_screen_radio.setChecked(settings["full_screen"])
            self.region_radio.setChecked(not settings["full_screen"])

            # 设置帧率
            index = self.fps_combo.findData(settings["fps"])
            if index >= 0:
                self.fps_combo.setCurrentIndex(index)

            # 设置音频选项
            self.record_audio_check.setChecked(settings["record_audio"])
            self.record_system_audio_check.setChecked(settings["record_system_audio"])
            self.record_mic_check.setChecked(settings["record_mic"])

            # 开始新的录制
            self.start_recording()

            # 显示通知
            from notification import NotificationManager, NotificationType
            NotificationManager().show_notification(
                "已自动分割录制文件并开始新的录制",
                notification_type=NotificationType.INFO,
                duration=5000,
                parent=self
            )

        except Exception as e:
            logger.error(f"开始新录制时发生错误: {str(e)}")

    def show_storage_warning(self, message):
        """显示存储警告"""
        from notification import NotificationManager, NotificationType
        NotificationManager().show_notification(
            message,
            notification_type=NotificationType.WARNING,
            duration=10000,
            parent=self
        )

    def show_cleanup_notification(self, deleted_count, freed_space):
        """显示清理完成通知"""
        from notification import NotificationManager, NotificationType
        NotificationManager().show_notification(
            f"已自动清理 {deleted_count} 个旧文件，释放了 {freed_space:.2f}GB 空间",
            notification_type=NotificationType.INFO,
            duration=5000,
            parent=self
        )

    def update_settings(self):
        """更新设置（从SettingsUI调用）"""
        try:
            # 从QSettings重新加载设置
            self.load_settings()

            # 更新存储管理器设置
            max_storage_time = self.settings.value("max_storage_time", 24, type=int)
            max_folder_size = self.settings.value("max_folder_size", 30, type=int)
            disk_limit = self.settings.value("disk_limit", 5, type=int)

            self.storage_manager.update_settings(
                max_time=max_storage_time,
                max_size=max_folder_size,
                disk_limit=disk_limit
            )

            # 更新单视频文件时长限制
            self.max_video_duration = self.settings.value("duration", 30, type=int)

            logger.info("已从SettingsUI更新设置")

        except Exception as e:
            logger.error(f"更新设置失败: {str(e)}")

    def apply_settings(self):
        """应用设置（已被SettingsUI替代，保留用于兼容）"""
        try:
            # 显示通知
            from notification import NotificationManager, NotificationType
            NotificationManager().show_notification(
                "请使用新的设置界面应用设置",
                notification_type=NotificationType.INFO,
                duration=5000,
                parent=self
            )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用设置失败: {str(e)}")
            logger.error(f"应用设置失败: {str(e)}")