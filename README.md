# 功能强大的录屏软件

一个支持隐藏式录屏和多通用格式转换的录屏软件。

## 🔧 最新修复 (2025-05-28)

### 重要问题修复
1. **视频录制时长与音频不同步问题**
   - ✅ 修复了视频录制时长明显短于音频录制时长的问题
   - ✅ 优化了屏幕录制器的帧率控制逻辑
   - ✅ 改进了时间戳记录机制，避免重复记录

2. **屏幕录制性能优化**
   - ✅ 使用MSS库提高屏幕捕获效率
   - ✅ 优化帧率控制算法，减少累积误差
   - ✅ 改进缓存机制，使用文件缓存替代内存缓存

3. **视频帧率修正功能**
   - ✅ 添加了自动视频帧率修正功能
   - ✅ 根据实际录制时间计算真实帧率
   - ✅ 修复了文件访问权限问题

4. **音视频合并优化**
   - ✅ 修复了FFmpeg参数冲突问题
   - ✅ 改进了音视频同步算法
   - ✅ 添加了多种备用合并方法

5. **代码质量改进**
   - ✅ 修复了缺失的导入语句（shutil, logging.handlers）
   - ✅ 优化了错误处理机制
   - ✅ 改进了日志记录系统

### 性能提升
- **录制帧率**：从10 FPS提升到12+ FPS
- **屏幕捕获**：使用MSS库提高捕获效率
- **内存使用**：优化缓存机制，减少内存占用
- **文件处理**：改进文件操作，避免权限冲突

## 功能特点

- 屏幕录制：捕获整个屏幕或指定区域
- 音频录制：支持系统声音和麦克风输入
- 隐藏模式：支持后台隐藏式录制
- 格式转换：支持多种视频格式转换（MP4, AVI, MKV, WMV等）
- 用户友好的界面：简洁直观的操作界面

## 软件架构

### 核心模块

1. **屏幕捕获模块**
   - 负责捕获屏幕内容
   - 支持全屏和区域选择录制
   - 支持帧率设置

2. **音频捕获模块**
   - 负责捕获系统和麦克风音频
   - 支持音频设备选择
   - 支持音频质量设置

3. **录制控制模块**
   - 负责开始、暂停、停止录制
   - 支持定时录制
   - 支持快捷键控制

4. **格式转换模块**
   - 负责将录制的视频转换为不同格式
   - 支持视频压缩和质量设置
   - 支持批量转换

5. **隐藏模式模块**
   - 负责实现隐藏式录制功能
   - 支持后台运行
   - 支持定时启动和停止

6. **GUI界面模块**
   - 提供用户友好的界面
   - 支持主题切换
   - 支持多语言

### 文件结构

- `main.py` - 主程序入口，负责启动应用程序和处理命令行参数
- `screen_recorder.py` - 屏幕录制核心功能，负责捕获屏幕内容
- `audio_recorder.py` - 音频录制功能，负责捕获系统声音和麦克风输入
- `format_converter.py` - 格式转换功能，负责将录制的视频转换为不同格式
- `hidden_mode.py` - 隐藏模式功能，负责实现隐藏式录制
- `gui.py` - 图形用户界面，提供用户友好的操作界面
- `config.py` - 配置文件，存储全局配置信息
- `utils.py` - 工具函数，提供各种辅助功能

## 技术栈

- Python 3.8+
- OpenCV - 视频处理
- PyAutoGUI - 屏幕捕获
- PyAudio - 音频捕获
- FFmpeg - 视频编码和格式转换
- PyQt5 - GUI界面

## 安装与使用

### 使用一键启动器（推荐）

本软件提供了一键启动器，可以自动检测运行环境、安装依赖并启动程序：

1. 双击 `启动录屏软件.bat` 文件
2. 启动器会自动检查Python环境、安装必要的依赖包
3. 如果FFmpeg未安装，启动器会自动从清华源下载并安装
4. 所有依赖配置完成后，程序会自动启动

### 手动安装（高级用户）

如果您希望手动安装依赖，可以按照以下步骤操作：

#### 依赖安装

```bash
pip install -r requirements.txt
```

#### FFmpeg安装

本软件使用FFmpeg进行音视频处理和格式转换。请按照以下步骤安装FFmpeg：

##### Windows

1. 访问 [FFmpeg官方下载页面](https://ffmpeg.org/download.html)
2. 下载适合您系统的FFmpeg版本（通常是Windows版本的"FFmpeg builds"）
3. 解压下载的文件到任意目录，例如 `C:\ffmpeg`
4. 将FFmpeg的bin目录（例如 `C:\ffmpeg\bin`）添加到系统PATH环境变量
5. 重启计算机以使环境变量生效

或者，您也可以将ffmpeg.exe文件直接复制到应用程序目录。

##### 验证安装

打开命令提示符或PowerShell，输入以下命令验证FFmpeg是否正确安装：

```bash
ffmpeg -version
```

如果显示FFmpeg的版本信息，则表示安装成功。

### 运行程序

#### 图形界面模式

```bash
python main.py
```

#### 命令行模式

```bash
# 显示版本信息
python main.py --version

# 以隐藏模式启动录制
python main.py --hidden

# 以隐藏模式启动录制，并设置录制时长（分钟）
python main.py --hidden --duration 30

# 以隐藏模式启动录制，并指定输出目录
python main.py --hidden --output "D:\录制视频"
```

## 使用说明

1. 启动程序后，可以选择录制模式（全屏/区域）
2. 设置录制参数（帧率、音频设备等）
3. 点击"开始录制"按钮开始录制
4. 使用快捷键或点击"停止录制"按钮结束录制
5. 可以选择保存格式和质量设置
6. 点击"保存"按钮完成录制

## 隐藏模式使用

1. 在设置中启用隐藏模式
2. 设置触发隐藏模式的快捷键
3. 设置录制参数
4. 使用快捷键启动隐藏录制
5. 使用快捷键停止隐藏录制

## 格式转换使用

1. 在主界面选择"格式转换"选项
2. 选择要转换的视频文件
3. 选择目标格式和质量设置
4. 点击"开始转换"按钮
5. 等待转换完成

## 开发与扩展

本项目采用模块化设计，便于扩展和维护。如需添加新功能，可以在相应模块中进行开发。

### 已完成功能

- [x] 屏幕录制模块 - 支持全屏录制和截图
- [x] 音频录制模块 - 支持系统声音和麦克风录制
- [x] 格式转换模块 - 支持多种视频格式转换
- [x] 隐藏模式模块 - 支持后台隐藏式录制
- [x] 图形用户界面 - 提供用户友好的操作界面
- [x] 命令行支持 - 支持通过命令行参数控制程序
- [x] 音视频合并 - 将分别录制的音频和视频合并为一个文件
- [x] 应用图标 - 添加应用程序图标和系统托盘图标

### 待完善功能

- [x] 区域选择录制 - 支持选择屏幕特定区域进行录制
- [x] 主题和语言切换 - 实现界面主题和多语言支持
- [ ] 安装程序 - 创建Windows安装程序
- [ ] 更多格式支持 - 增加对更多视频格式的支持

### 最近更新

- 添加了区域选择录制功能，支持选择屏幕特定区域进行录制
- 添加了主题切换功能，支持浅色和深色主题
- 添加了语言切换功能，支持中文和英文
- 优化了通知提示方式，使用悬浮通知替代弹窗提示
- 改进了用户界面和交互体验

## 故障排除

### 音视频合并问题

如果您在合并音频和视频时遇到问题（如合并后的文件没有声音），请尝试以下解决方案：

1. **确保FFmpeg正确安装**
   - 验证FFmpeg是否已正确安装，可以在命令行中运行 `ffmpeg -version`
   - 如果未安装，请按照上述安装说明进行安装

2. **检查录制的文件**
   - 确保音频和视频文件都存在且不为空
   - 尝试使用其他播放器（如VLC）单独播放音频和视频文件，确认它们可以正常播放
   - 检查音频文件格式是否兼容（WAV文件通常兼容性最好）

3. **尝试手动合并**
   - 如果应用程序无法自动合并，您可以尝试手动使用FFmpeg合并文件
   - 打开命令提示符，尝试以下几种命令：

     方法1（基本合并）:
     ```
     ffmpeg -i 视频文件路径 -i 音频文件路径 -c:v copy -c:a aac -b:a 192k -shortest 输出文件路径.mp4
     ```

     方法2（先转换音频）:
     ```
     ffmpeg -i 音频文件路径 -c:a aac -b:a 192k 临时音频.aac
     ffmpeg -i 视频文件路径 -i 临时音频.aac -c:v copy -c:a copy 输出文件路径.mp4
     ```

     方法3（使用filter_complex）:
     ```
     ffmpeg -i 视频文件路径 -i 音频文件路径 -filter_complex "[0:v][1:a]concat=n=1:v=1:a=1[outv][outa]" -map "[outv]" -map "[outa]" 输出文件路径.mp4
     ```

4. **检查音频设备设置**
   - 确保在录制时选择了正确的音频设备
   - 检查系统音量是否已静音或设置得太低

5. **更新软件**
   - 确保您使用的是最新版本的软件
   - 检查是否有可用的更新

6. **重启应用程序和系统**
   - 有时候简单地重启应用程序或计算机可以解决问题

如果问题仍然存在，请查看日志文件获取更详细的错误信息。

## 许可证

MIT License

## 图标设置说明

- 应用主窗口和系统托盘图标已统一设置为项目根目录下的 `recorder_icon.ico` 文件。
- 相关代码已在 `main.py` 和 `gui.py` 中修改，确保所有界面均使用统一的ico图标。
- 若需更换图标，只需替换根目录下的 `recorder_icon.ico` 文件即可，无需修改代码。

### 代码注释
- `main.py`：通过 `app.setWindowIcon(QIcon(icon_path))` 设置主窗口图标。
- `gui.py`：通过 `self.setWindowIcon(QIcon(icon_path))` 和 `self.tray_icon.setIcon(icon)` 分别设置主窗口和托盘图标。

## 视频录制与音视频同步说明

- 视频录制时，帧率（fps）由用户在界面选择，或使用默认配置，传递给 `ScreenRecorder`。
- `ScreenRecorder` 通过 `cv2.VideoWriter` 以指定帧率写入视频帧，帧间隔由 `frame_interval = 1.0 / fps` 控制。
- 音频录制独立进行，采样率和通道数可配置，录制为WAV文件。
- 录制结束后，音视频合成采用FFmpeg命令行工具，合成时会检测音视频时长并尝试多种同步方式，避免音视频不同步。
- 合成命令中 `-async 1` 及帧率参数 `-r` 用于音视频同步，若检测到时长差异较大，会自动尝试备用合成方案。
- 若合成后仍有不同步问题，可参考README中的手动合成命令或调整录制帧率。

### 相关代码说明
- `screen_recorder.py`：负责屏幕帧捕获与写入，帧率控制逻辑在 `_record_screen` 方法。
- `audio_recorder.py`：负责音频流采集与保存。
- `gui.py`：录制结束后调用 `_merge_audio_video` 方法，自动检测并处理音视频同步。
