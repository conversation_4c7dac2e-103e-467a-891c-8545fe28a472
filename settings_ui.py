#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
设置界面模块 - 提供录屏软件的设置界面
"""

import os
import sys
import logging
import shutil
import psutil
from PyQt5.QtWidgets import (QWidget, QLabel, QLineEdit, QCheckBox, QRadioButton, 
                            QPushButton, QVBoxLayout, QHBoxLayout, QGridLayout, 
                            QGroupBox, QFileDialog, QMessageBox, QSpinBox)
from PyQt5.QtCore import Qt, QSettings, QTimer
from PyQt5.QtGui import QIntValidator, QDoubleValidator

import config
import utils

# 设置日志
logger = logging.getLogger(__name__)

class SettingsUI(QWidget):
    """设置界面类"""
    
    def __init__(self, parent=None):
        """初始化设置界面"""
        super().__init__(parent)
        self.parent = parent
        self.settings = QSettings("ScreenRecorder", "Settings")
        self.init_ui()
        self.load_settings()
        
        # 定时更新硬盘信息
        self.disk_timer = QTimer(self)
        self.disk_timer.timeout.connect(self.update_disk_info)
        self.disk_timer.start(10000)  # 每10秒更新一次

    def update_fps(self, value):
        """更新全局帧率配置并通知录制器"""
        config.SCREEN_RECORD_CONFIG["fps"] = value
        if self.parent and hasattr(self.parent, 'screen_recorder'):
            self.parent.screen_recorder.update_fps(value)
        logger.info(f"FPS设置更新为: {value}")
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("系统设置")
        
        # 主布局
        main_layout = QVBoxLayout()
        
        # 系统设置组
        system_group = QGroupBox("系统设置")
        system_layout = QGridLayout()
        
        # 单视频文件时长
        system_layout.addWidget(QLabel("单视频文件时长(分)"), 0, 0)
        self.duration_spinbox = QSpinBox()
        self.duration_spinbox.setRange(1, 120)
        self.duration_spinbox.setValue(30)
        system_layout.addWidget(self.duration_spinbox, 0, 1)
        
        # 开机自启
        self.autostart_checkbox = QCheckBox("是否开机自启")
        system_layout.addWidget(self.autostart_checkbox, 0, 2)
        
        # 存储最大时间
        system_layout.addWidget(QLabel("存储最大时间(时)"), 1, 0)
        self.max_storage_time_spinbox = QSpinBox()
        self.max_storage_time_spinbox.setRange(1, 168)  # 最多7天
        self.max_storage_time_spinbox.setValue(24)
        system_layout.addWidget(self.max_storage_time_spinbox, 1, 1)
        
        # 文件夹最大容量
        system_layout.addWidget(QLabel("文件夹最大容量(GB)"), 2, 0)
        self.max_folder_size_spinbox = QSpinBox()
        self.max_folder_size_spinbox.setRange(1, 1000)
        self.max_folder_size_spinbox.setValue(30)
        system_layout.addWidget(self.max_folder_size_spinbox, 2, 1)
        
        # 录制质量
        quality_layout = QHBoxLayout()
        self.low_quality_radio = QRadioButton("低帧率(4)")
        self.high_quality_radio = QRadioButton("高帧率(7)")
        self.high_quality_radio.setChecked(True)
        quality_layout.addWidget(self.low_quality_radio)
        quality_layout.addWidget(self.high_quality_radio)
        system_layout.addLayout(quality_layout, 3, 0, 1, 2)

        # 帧率设置
        system_layout.addWidget(QLabel("帧率(FPS)", self), 4, 0)
        self.fps_spinbox = QSpinBox(self)
        self.fps_spinbox.setRange(1, 60)
        self.fps_spinbox.setValue(config.SCREEN_RECORD_CONFIG["fps"])
        self.fps_spinbox.valueChanged.connect(self.update_fps)
        system_layout.addWidget(self.fps_spinbox, 4, 1)
        
        # 硬盘空间信息
        system_layout.addWidget(QLabel("硬盘空间监控(GB)"), 4, 0)
        self.disk_limit_spinbox = QSpinBox()
        self.disk_limit_spinbox.setRange(1, 1000)
        self.disk_limit_spinbox.setValue(5)
        system_layout.addWidget(self.disk_limit_spinbox, 4, 1)
        
        self.disk_info_label = QLabel("硬盘剩余容量(GB): 计算中...")
        system_layout.addWidget(self.disk_info_label, 5, 0, 1, 2)
        
        system_group.setLayout(system_layout)
        main_layout.addWidget(system_group)
        
        # 路径设置
        path_layout = QHBoxLayout()
        self.path_button = QPushButton("路径选择")
        self.path_button.clicked.connect(self.select_output_path)
        self.save_button = QPushButton("保存应用")
        self.save_button.clicked.connect(self.save_settings)
        self.save_button.setStyleSheet("background-color: #4CAF50; color: white;")
        
        path_layout.addWidget(self.path_button)
        path_layout.addWidget(self.save_button)
        main_layout.addLayout(path_layout)
        
        self.setLayout(main_layout)
        
        # 初始更新硬盘信息
        self.update_disk_info()
        
    def update_disk_info(self):
        """更新硬盘空间信息"""
        try:
            output_dir = config.DEFAULT_OUTPUT_DIR
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
                
            disk_usage = shutil.disk_usage(output_dir)
            free_gb = disk_usage.free / (1024 ** 3)  # 转换为GB
            total_gb = disk_usage.total / (1024 ** 3)
            
            self.disk_info_label.setText(f"硬盘剩余容量(GB): {free_gb:.2f}")
            
            # 检查是否低于警戒线
            if free_gb < self.disk_limit_spinbox.value():
                self.disk_info_label.setStyleSheet("color: red; font-weight: bold;")
            else:
                self.disk_info_label.setStyleSheet("")
                
        except Exception as e:
            logger.error(f"更新硬盘信息失败: {str(e)}")
            self.disk_info_label.setText(f"硬盘剩余容量(GB): 获取失败")
            
    def select_output_path(self):
        """选择输出路径"""
        output_dir = QFileDialog.getExistingDirectory(
            self, "选择录制文件保存路径", 
            config.DEFAULT_OUTPUT_DIR
        )
        
        if output_dir:
            config.DEFAULT_OUTPUT_DIR = output_dir
            self.update_disk_info()
            logger.info(f"已更改输出目录: {output_dir}")
            
    def load_settings(self):
        """加载设置"""
        # 从QSettings加载
        self.duration_spinbox.setValue(self.settings.value("duration", 30, type=int))
        self.autostart_checkbox.setChecked(self.settings.value("autostart", False, type=bool))
        self.max_storage_time_spinbox.setValue(self.settings.value("max_storage_time", 24, type=int))
        self.max_folder_size_spinbox.setValue(self.settings.value("max_folder_size", 30, type=int))
        self.disk_limit_spinbox.setValue(self.settings.value("disk_limit", 5, type=int))
        
        # 录制质量
        if self.settings.value("high_quality", True, type=bool):
            self.high_quality_radio.setChecked(True)
        else:
            self.low_quality_radio.setChecked(True)
            
        # 从config加载
        if hasattr(config, "DEFAULT_OUTPUT_DIR"):
            self.output_dir = config.DEFAULT_OUTPUT_DIR
        else:
            self.output_dir = os.path.join(os.path.expanduser("~"), "Videos", "ScreenRecorder")
            
    def save_settings(self):
        """保存设置"""
        try:
            # 保存到QSettings
            self.settings.setValue("duration", self.duration_spinbox.value())
            self.settings.setValue("autostart", self.autostart_checkbox.isChecked())
            self.settings.setValue("max_storage_time", self.max_storage_time_spinbox.value())
            self.settings.setValue("max_folder_size", self.max_folder_size_spinbox.value())
            self.settings.setValue("high_quality", self.high_quality_radio.isChecked())
            self.settings.setValue("disk_limit", self.disk_limit_spinbox.value())
            
            # 更新config
            config.DEFAULT_OUTPUT_DIR = self.output_dir
            
            # 更新录制设置
            if self.high_quality_radio.isChecked():
                config.SCREEN_RECORD_CONFIG["fps"] = 30
            else:
                config.SCREEN_RECORD_CONFIG["fps"] = 15
                
            # 设置开机自启
            self.set_autostart(self.autostart_checkbox.isChecked())
            
            # 显示成功消息
            QMessageBox.information(self, "保存成功", "设置已成功保存并应用")
            logger.info("设置已保存")
            
            # 通知主窗口更新设置
            if self.parent and hasattr(self.parent, "update_settings"):
                self.parent.update_settings()
                
        except Exception as e:
            logger.error(f"保存设置失败: {str(e)}")
            QMessageBox.critical(self, "保存失败", f"保存设置时发生错误: {str(e)}")
            
    def set_autostart(self, enable):
        """设置开机自启"""
        try:
            if sys.platform == "win32":
                import winreg
                startup_key = winreg.OpenKey(
                    winreg.HKEY_CURRENT_USER,
                    r"Software\Microsoft\Windows\CurrentVersion\Run",
                    0, winreg.KEY_SET_VALUE
                )
                
                if enable:
                    # 获取当前程序路径
                    app_path = os.path.abspath(sys.argv[0])
                    if app_path.endswith('.py'):
                        # 如果是Python脚本，使用pythonw.exe运行
                        python_path = sys.executable.replace('python.exe', 'pythonw.exe')
                        command = f'"{python_path}" "{app_path}"'
                    else:
                        # 如果是可执行文件，直接运行
                        command = f'"{app_path}"'
                        
                    winreg.SetValueEx(startup_key, "ScreenRecorder", 0, winreg.REG_SZ, command)
                    logger.info("已设置开机自启")
                else:
                    try:
                        winreg.DeleteValue(startup_key, "ScreenRecorder")
                        logger.info("已取消开机自启")
                    except FileNotFoundError:
                        pass
                        
                winreg.CloseKey(startup_key)
        except Exception as e:
            logger.error(f"设置开机自启失败: {str(e)}")
