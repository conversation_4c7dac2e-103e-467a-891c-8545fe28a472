"""
格式转换模块 - 负责将录制的视频转换为不同格式
"""

import os
import time
import subprocess
import threading
import logging
from enum import Enum

import config
import utils

logger = logging.getLogger(__name__)

class ConversionStatus(Enum):
    """转换状态枚举"""
    IDLE = 0
    CONVERTING = 1
    COMPLETED = 2
    FAILED = 3
    CANCELLED = 4

class FormatConverter:
    """格式转换类"""
    
    def __init__(self):
        """初始化格式转换器"""
        self.ffmpeg_path = config.FORMAT_CONVERSION_CONFIG["ffmpeg_path"]
        self.supported_formats = config.FORMAT_CONVERSION_CONFIG["supported_formats"]
        self.default_format = config.FORMAT_CONVERSION_CONFIG["default_format"]
        self.use_hardware_acceleration = config.FORMAT_CONVERSION_CONFIG["use_hardware_acceleration"]
        self.preset = config.FORMAT_CONVERSION_CONFIG["preset"]
        
        # 转换状态
        self.status = ConversionStatus.IDLE
        self.progress = 0.0  # 0.0 - 100.0
        self.error_message = ""
        
        # 转换线程
        self.convert_thread = None
        self.process = None
        
        # 检查FFmpeg是否已安装
        self.ffmpeg_available = utils.check_ffmpeg_installed()
        if not self.ffmpeg_available:
            logger.warning("FFmpeg未安装或路径不正确，格式转换功能将不可用")
        
        logger.info(f"初始化格式转换器: FFmpeg可用={self.ffmpeg_available}")
    
    def convert_video(self, input_path, output_path=None, target_format=None, 
                      quality=None, width=None, height=None, fps=None, 
                      audio_bitrate=None, video_bitrate=None):
        """
        转换视频格式
        
        参数:
            input_path (str): 输入文件路径
            output_path (str): 输出文件路径，如果为None则根据输入路径和目标格式生成
            target_format (str): 目标格式，如果为None则使用默认格式
            quality (int): 视频质量 (0-100)，如果为None则使用默认质量
            width (int): 视频宽度，如果为None则保持原始宽度
            height (int): 视频高度，如果为None则保持原始高度
            fps (int): 帧率，如果为None则保持原始帧率
            audio_bitrate (str): 音频比特率，如果为None则使用默认比特率
            video_bitrate (str): 视频比特率，如果为None则使用默认比特率
        
        返回:
            bool: 是否成功启动转换
        """
        if not self.ffmpeg_available:
            logger.error("FFmpeg未安装或路径不正确，无法进行格式转换")
            self.error_message = "FFmpeg未安装或路径不正确"
            self.status = ConversionStatus.FAILED
            return False
        
        if self.status == ConversionStatus.CONVERTING:
            logger.warning("已有转换任务正在进行中")
            return False
        
        if not os.path.exists(input_path):
            logger.error(f"输入文件不存在: {input_path}")
            self.error_message = f"输入文件不存在: {input_path}"
            self.status = ConversionStatus.FAILED
            return False
        
        # 设置目标格式
        target_format = target_format or self.default_format
        if target_format not in self.supported_formats:
            logger.error(f"不支持的目标格式: {target_format}")
            self.error_message = f"不支持的目标格式: {target_format}"
            self.status = ConversionStatus.FAILED
            return False
        
        # 设置输出路径
        if output_path is None:
            input_dir = os.path.dirname(input_path)
            input_name = os.path.splitext(os.path.basename(input_path))[0]
            output_path = os.path.join(input_dir, f"{input_name}_converted.{target_format}")
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        os.makedirs(output_dir, exist_ok=True)
        
        # 重置状态
        self.status = ConversionStatus.CONVERTING
        self.progress = 0.0
        self.error_message = ""
        
        # 启动转换线程
        self.convert_thread = threading.Thread(
            target=self._convert_video_thread,
            args=(input_path, output_path, target_format, quality, width, height, fps, audio_bitrate, video_bitrate)
        )
        self.convert_thread.daemon = True
        self.convert_thread.start()
        
        logger.info(f"开始转换视频: {input_path} -> {output_path}")
        return True
    
    def _convert_video_thread(self, input_path, output_path, target_format, 
                             quality, width, height, fps, audio_bitrate, video_bitrate):
        """转换视频的线程函数"""
        try:
            # 构建FFmpeg命令
            command = [self.ffmpeg_path, "-i", input_path]
            
            # 视频编码器
            if target_format == "mp4":
                command.extend(["-c:v", "libx264"])
            elif target_format == "webm":
                command.extend(["-c:v", "libvpx-vp9"])
            elif target_format == "mkv":
                command.extend(["-c:v", "libx264"])
            elif target_format == "avi":
                command.extend(["-c:v", "mpeg4"])
            elif target_format == "mov":
                command.extend(["-c:v", "libx264"])
            elif target_format == "flv":
                command.extend(["-c:v", "flv"])
            elif target_format == "wmv":
                command.extend(["-c:v", "wmv2"])
            
            # 硬件加速
            if self.use_hardware_acceleration:
                # 尝试使用NVIDIA GPU加速
                try:
                    # 检查是否有NVIDIA GPU
                    nvidia_check = subprocess.run(
                        ["nvidia-smi"], 
                        stdout=subprocess.PIPE, 
                        stderr=subprocess.PIPE,
                        check=False
                    )
                    if nvidia_check.returncode == 0:
                        command = [self.ffmpeg_path, "-hwaccel", "cuda", "-i", input_path]
                        if target_format in ["mp4", "mkv", "mov"]:
                            command.extend(["-c:v", "h264_nvenc"])
                except:
                    pass
            
            # 视频质量
            if quality is not None:
                if target_format in ["mp4", "mkv", "mov"]:
                    # 将0-100的质量值映射到CRF值（0-51，值越小质量越高）
                    crf = int(51 - (quality / 100 * 51))
                    command.extend(["-crf", str(crf)])
                else:
                    # 对于其他格式，使用比特率控制
                    bitrate = int(1000 + (quality / 100 * 9000))  # 1000-10000 kbps
                    command.extend(["-b:v", f"{bitrate}k"])
            
            # 分辨率
            if width is not None and height is not None:
                command.extend(["-s", f"{width}x{height}"])
            
            # 帧率
            if fps is not None:
                command.extend(["-r", str(fps)])
            
            # 音频比特率
            if audio_bitrate is not None:
                command.extend(["-b:a", audio_bitrate])
            else:
                command.extend(["-b:a", "128k"])  # 默认音频比特率
            
            # 视频比特率
            if video_bitrate is not None:
                command.extend(["-b:v", video_bitrate])
            
            # 编码预设
            command.extend(["-preset", self.preset])
            
            # 输出文件
            command.extend(["-y", output_path])
            
            logger.debug(f"FFmpeg命令: {' '.join(command)}")
            
            # 执行FFmpeg命令
            self.process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1
            )
            
            # 获取输入视频的总时长
            duration_cmd = [
                self.ffmpeg_path, 
                "-i", input_path, 
                "-hide_banner"
            ]
            duration_output = subprocess.check_output(
                duration_cmd, 
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            
            # 解析视频时长
            duration_seconds = 0
            for line in duration_output.split('\n'):
                if "Duration" in line:
                    time_str = line.split("Duration: ")[1].split(",")[0].strip()
                    h, m, s = time_str.split(":")
                    duration_seconds = int(h) * 3600 + int(m) * 60 + float(s)
                    break
            
            # 监控转换进度
            for line in self.process.stderr:
                if "time=" in line:
                    time_str = line.split("time=")[1].split(" ")[0].strip()
                    if ":" in time_str:
                        h, m, s = time_str.split(":")
                        current_seconds = int(h) * 3600 + int(m) * 60 + float(s)
                        if duration_seconds > 0:
                            self.progress = min(100.0, (current_seconds / duration_seconds) * 100)
            
            # 等待进程完成
            self.process.wait()
            
            # 检查进程返回码
            if self.process.returncode == 0:
                self.status = ConversionStatus.COMPLETED
                self.progress = 100.0
                logger.info(f"视频转换完成: {output_path}")
            else:
                self.status = ConversionStatus.FAILED
                self.error_message = "FFmpeg进程返回非零退出码"
                logger.error(f"视频转换失败: {self.error_message}")
        
        except Exception as e:
            self.status = ConversionStatus.FAILED
            self.error_message = str(e)
            logger.error(f"视频转换过程中发生错误: {str(e)}")
        
        finally:
            self.process = None
    
    def cancel_conversion(self):
        """取消转换"""
        if self.status != ConversionStatus.CONVERTING:
            logger.warning("没有正在进行的转换任务")
            return False
        
        try:
            if self.process:
                self.process.terminate()
                self.status = ConversionStatus.CANCELLED
                logger.info("已取消视频转换")
                return True
        
        except Exception as e:
            logger.error(f"取消视频转换失败: {str(e)}")
            return False
    
    def get_conversion_progress(self):
        """获取转换进度"""
        return {
            "status": self.status,
            "progress": self.progress,
            "error_message": self.error_message
        }
    
    def is_format_supported(self, format_name):
        """检查格式是否支持"""
        return format_name.lower() in self.supported_formats
