"""
屏幕录制模块 - 负责捕获屏幕内容并保存为视频
"""

import os
import time
import threading
import numpy as np
import cv2
import pyautogui
from PIL import ImageGrab
import logging
import subprocess

import config
import utils

logger = logging.getLogger(__name__)

class ScreenRecorder:
    """屏幕录制类"""

    def __init__(self, output_path=None, region=None, fps=None, codec=None):
        """
        初始化屏幕录制器

        参数:
            output_path (str): 输出文件路径，如果为None则使用默认路径
            region (tuple): 录制区域 (left, top, width, height)，如果为None则全屏录制
            fps (int): 帧率，如果为None则使用默认帧率
            codec (str): 视频编码器，如果为None则使用默认编码器
        """
        self.output_path = output_path or utils.get_output_file_path()
        self.fps = fps or config.SCREEN_RECORD_CONFIG["fps"]
        self.frame_interval = 1.0 / self.fps
        self.codec = codec or config.SCREEN_RECORD_CONFIG["codec"]

        # 如果region为None，则录制全屏
        if region is None:
            screen_width, screen_height = utils.get_screen_resolution()
            self.region = (0, 0, screen_width, screen_height)
        else:
            self.region = region

        # 视频编码器
        if self.codec == "XVID" and self.output_path.lower().endswith(".mp4"):
            # 对于MP4文件，使用H.264编码器
            self.codec = "mp4v"
            logger.info(f"对MP4文件使用mp4v编码器替代XVID")
        self.fourcc = cv2.VideoWriter_fourcc(*self.codec)

        # 录制状态
        self.is_recording = False
        self.is_paused = False
        self.start_time = 0
        self.elapsed_time = 0
        self.pause_time = 0

        # 录制线程
        self.record_thread = None

        # 视频写入器
        self.out = None

        self.frame_timestamps = []
        
        # 确保输出目录存在
        output_dir = os.path.dirname(self.output_path)
        os.makedirs(output_dir, exist_ok=True)

        logger.info(f"初始化屏幕录制器: 输出路径={self.output_path}, 区域={self.region}, FPS={self.fps}")

    def update_fps(self, new_fps):
        """更新帧率并调整帧间隔"""
        self.fps = new_fps
        self.frame_interval = 1.0 / new_fps
        logger.info(f"更新录制帧率为: {new_fps} FPS，帧间隔调整为: {self.frame_interval:.4f} 秒")

    def start_recording(self):
        """开始录制"""
        if self.is_recording:
            logger.warning("录制已经在进行中")
            return False

        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(self.output_path)
            os.makedirs(output_dir, exist_ok=True)

            # 创建视频写入器
            width, height = self.region[2], self.region[3]
            self.out = cv2.VideoWriter(
                self.output_path,
                self.fourcc,
                self.fps,
                (width, height)
            )

            # 设置录制状态
            self.is_recording = True
            self.is_paused = False
            self.start_time = time.time()

            # 启动录制线程
            self.record_thread = threading.Thread(target=self._record_screen)
            self.record_thread.daemon = True
            self.record_thread.start()

            logger.info(f"开始录制: {self.output_path}")
            return True

        except Exception as e:
            logger.error(f"开始录制失败: {str(e)}")
            return False

    def pause_recording(self):
        """暂停录制"""
        if not self.is_recording:
            logger.warning("没有正在进行的录制")
            return False

        if self.is_paused:
            # 恢复录制
            self.is_paused = False
            self.elapsed_time += time.time() - self.pause_time
            logger.info("恢复录制")
            return True
        else:
            # 暂停录制
            self.is_paused = True
            self.pause_time = time.time()
            logger.info("暂停录制")
            return True

    def stop_recording(self):
        """
        停止录制，将缓存帧压制为视频，然后与音频合并
        """
        if not self.is_recording:
            logger.warning("没有正在进行的录制")
            return False
        
        self.is_recording = False
        self.is_paused = False
        if self.record_thread and self.record_thread.is_alive():
            self.record_thread.join(timeout=2.0)
        
        if not self.is_paused:
            self.elapsed_time += time.time() - self.start_time
        
        # 获取缓存文件夹路径
        cache_dir = os.path.join(os.path.dirname(self.output_path), "frame_cache")
        
        # 1. 将缓存帧压制为视频
        if os.path.exists(cache_dir):
            # 按文件名排序帧文件
            frame_files = sorted([f for f in os.listdir(cache_dir) if f.startswith("frame_")])
            
            if frame_files:
                # 创建视频写入器
                width, height = self.region[2], self.region[3]
                fourcc = cv2.VideoWriter_fourcc(*self.codec)
                out = cv2.VideoWriter(self.output_path, fourcc, self.fps, (width, height))
                
                # 读取并写入所有帧
                for frame_file in frame_files:
                    frame_path = os.path.join(cache_dir, frame_file)
                    frame = cv2.imread(frame_path)
                    out.write(frame)
                    
                out.release()
                logger.info(f"视频已从缓存帧生成: {self.output_path}")
                
                # 删除缓存文件夹
                shutil.rmtree(cache_dir)
            else:
                logger.warning("缓存文件夹中没有找到任何帧文件")
        
        logger.info(f"停止录制: {self.output_path}, 录制时间: {utils.format_time(self.elapsed_time)}")
        # 校验帧数与时长
        cap = cv2.VideoCapture(self.output_path)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        duration = frame_count / fps if fps > 0 else 0
        cap.release()
        logger.info(f"录制文件帧数: {frame_count}, 文件FPS: {fps}, 文件时长: {duration:.2f}秒")
        # 判断是否需要修正
        expected_frames = int(self.fps * self.elapsed_time)
        if abs(frame_count - expected_frames) > max(2, 0.05 * expected_frames):
            logger.warning(f"检测到帧数与预期不符，尝试修正视频帧率...")
            self.fix_video_fps()
        return True

    def _record_screen(self):
        """
        录制屏幕的线程函数，精确控制帧率，动态修正时间，定期输出实际帧率日志
        优化点：
        1. 使用更高效的帧捕获和写入方式
        2. 改进时间控制算法，减少累积误差
        3. 增加性能监控和自适应调整
        """
        logger.debug("录制线程启动")
        left, top, width, height = self.region
        frame_interval = self.frame_interval
        next_frame_time = time.time()
        frame_count = 0
        start_time = time.time()
        last_log_time = start_time
        
        # 预分配内存用于帧捕获
        frame_buffer = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 创建缓存文件夹
        cache_dir = os.path.join(os.path.dirname(self.output_path), "frame_cache")
        os.makedirs(cache_dir, exist_ok=True)
        
        try:
            while self.is_recording:
                # 计算当前帧的理论捕获时间
                expected_capture_time = next_frame_time
                current_time = time.time()
                
                # 处理时间延迟：如果当前时间超过理论时间，直接捕获（不补帧）
                if current_time >= expected_capture_time:
                    # 使用mss加速屏幕捕获（需安装mss库）
                    import mss
                    with mss.mss() as sct:
                        monitor = {'top': top, 'left': left, 'width': width, 'height': height}
                        sct_img = sct.grab(monitor)
                        frame = np.array(sct_img)
                        frame = cv2.cvtColor(frame, cv2.COLOR_RGBA2BGR)  # MSS返回RGBA格式
                    
                    # 保存帧到缓存文件夹
                    timestamp = time.strftime("%Y%m%d_%H%M%S", time.localtime(current_time))
                    frame_filename = f"frame_{frame_count:06d}_{timestamp}.png"
                    frame_path = os.path.join(cache_dir, frame_filename)
                    cv2.imwrite(frame_path, frame)
                    
                    frame_count += 1
                    
                    # 记录实际捕获时间戳
                    actual_capture_time = time.time()
                    self.frame_timestamps.append(actual_capture_time)
                    
                    # 计算捕获耗时并调整下一帧时间
                    capture_duration = actual_capture_time - expected_capture_time
                    next_frame_time = expected_capture_time + frame_interval
                    
                    # 定期输出实际FPS日志
                    if current_time - last_log_time >= 1.0:  # 每秒输出一次
                        actual_fps = frame_count / (current_time - start_time)
                        logger.info(f"录制帧数: {frame_count}, 实际FPS: {actual_fps:.2f}")
                        last_log_time = current_time
                else:
                    # 精确睡眠到理论帧时间
                    sleep_duration = expected_capture_time - current_time
                    if sleep_duration > 0.001:  # 只对显著延迟进行睡眠
                        time.sleep(min(sleep_duration, frame_interval * 0.3))

                # 处理暂停状态
                if self.is_paused:
                    next_frame_time = time.time() + frame_interval  # 暂停后重置时间基准
                    time.sleep(0.1)
                    continue

                # 每10帧记录一次时间漂移
                if frame_count % 10 == 0:
                    current_drift = time.time() - next_frame_time
                    logger.debug(f"帧时间漂移: {current_drift:.4f}s, 理论间隔: {frame_interval:.4f}s")
                # 记录帧时间戳
                self.frame_timestamps.append(time.time())
                # 每30帧输出一次实际帧率
                if frame_count % 30 == 0:
                    elapsed = time.time() - start_time
                    actual_fps = frame_count / elapsed if elapsed > 0 else 0
                    logger.info(f"录制帧数: {frame_count}, 实际FPS: {actual_fps:.2f}")
        except Exception as e:
            logger.error(f"录制过程中发生错误: {str(e)}")
            self.is_recording = False
        finally:
            logger.debug("录制线程结束")

    def get_recording_time(self):
        """获取当前录制时间（秒）"""
        if not self.is_recording:
            return self.elapsed_time

        if self.is_paused:
            return self.elapsed_time

        current_time = time.time()
        return self.elapsed_time + (current_time - self.start_time)

    def take_screenshot(self, output_path=None):
        """
        捕获当前屏幕截图

        参数:
            output_path (str): 输出文件路径，如果为None则使用默认路径

        返回:
            str: 截图保存路径
        """
        if output_path is None:
            output_path = utils.get_output_file_path(prefix="screenshot", extension="png")

        try:
            left, top, width, height = self.region
            screenshot = ImageGrab.grab(bbox=(left, top, left+width, top+height))
            screenshot.save(output_path)
            logger.info(f"截图已保存: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"截图失败: {str(e)}")
            return None

    def get_average_frame_duration(self):
        """
        计算平均每帧保持的时长（秒）
        """
        if len(self.frame_timestamps) < 2:
            return 0
        intervals = [
            self.frame_timestamps[i+1] - self.frame_timestamps[i]
            for i in range(len(self.frame_timestamps) - 1)
        ]
        return sum(intervals) / len(intervals)

    def fix_video_fps(self, keep_original=False):
        """
        用实际帧率重新打包视频，确保播放时长与录制时长一致
        keep_original: 是否保留原始文件，默认不保留
        """
        avg_duration = self.get_average_frame_duration()
        if avg_duration > 0:
            real_fps = 1.0 / self.frame_interval
            input_path = self.output_path
            if keep_original:
                output_path = input_path.replace('.mp4', '_fixed.mp4')
            else:
                output_path = input_path + '.tmp.mp4'
            ffmpeg_path = config.FORMAT_CONVERSION_CONFIG["ffmpeg_path"]
            cmd = [
                ffmpeg_path, '-y',
                '-i', input_path,
                '-r', str(real_fps),
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '23',
                '-an',  # 不处理音频
                output_path
            ]
            logger.info(f"执行FFmpeg命令: {' '.join(cmd)})")
            subprocess.run(cmd)
            # 校验修正后的视频
            cap = cv2.VideoCapture(output_path)
            fixed_frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fixed_fps = cap.get(cv2.CAP_PROP_FPS)
            fixed_duration = fixed_frame_count / fixed_fps if fixed_fps > 0 else 0
            cap.release()
            logger.info(f"修正后帧数: {fixed_frame_count}, FPS: {fixed_fps}, 时长: {fixed_duration:.2f}秒")
            # 替换原文件
            if not keep_original:
                os.replace(output_path, input_path)
                logger.info(f"已用实际fps({real_fps:.2f})覆盖原视频: {input_path}")
            else:
                logger.info(f"已用实际fps({real_fps:.2f})生成新视频: {output_path}")
            return output_path
        return None

    def set_fps(self, new_fps):
        """设置新的帧率，并自动更新帧间隔"""
        self.fps = new_fps
        self.frame_interval = 1.0 / self.fps
        logger.info(f"帧率已更新为{self.fps}，帧间隔为{self.frame_interval:.4f}秒")
