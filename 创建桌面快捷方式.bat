@echo off
echo 正在创建桌面快捷方式...

:: 确保图标存在
if not exist "%~dp0recorder_icon.ico" (
    echo 创建应用图标...
    python "%~dp0create_icon.py"
)

:: 创建VBS脚本来创建快捷方式
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateShortcut.vbs"
echo sLinkFile = oWS.SpecialFolders("Desktop") ^& "\录屏软件.lnk" >> "%TEMP%\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateShortcut.vbs"
echo oLink.TargetPath = "%~dp0启动录屏软件.bat" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "%~dp0" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Description = "功能强大的录屏软件" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.IconLocation = "%~dp0recorder_icon.ico" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateShortcut.vbs"

:: 执行VBS脚本
cscript //nologo "%TEMP%\CreateShortcut.vbs"
del "%TEMP%\CreateShortcut.vbs"

echo 桌面快捷方式已创建！
pause
