#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
录屏软件一键启动器
- 自动检测运行环境
- 检查依赖配置是否正常
- 若缺失则自动从清华源进行安装配置
- 配置完成后自动启动程序
"""

import os
import sys
import subprocess
import platform
import time
import logging
import tempfile
import shutil
import re
import json
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("启动器")

# 清华源镜像地址
TSINGHUA_MIRROR = "https://pypi.tuna.tsinghua.edu.cn/simple"

# 必需的Python包
REQUIRED_PACKAGES = [
    "opencv-python>=4.5.0",
    "numpy>=1.20.0",
    "pyautogui>=0.9.52",
    "pyaudio>=0.2.11",
    "pillow>=8.0.0",
    "ffmpeg-python>=0.2.0",
    "pyqt5>=5.15.0",
    "keyboard>=0.13.5",
    "mouse>=0.7.1",
    "pynput>=1.7.3"
]

# FFmpeg下载地址
FFMPEG_DOWNLOAD_URL = "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip"
FFMPEG_MIRROR_URL = "https://mirrors.tuna.tsinghua.edu.cn/github-release/BtbN/FFmpeg-Builds/latest/ffmpeg-master-latest-win64-gpl.zip"

def check_python_version():
    """检查Python版本是否满足要求"""
    logger.info("检查Python版本...")
    major, minor, _ = platform.python_version_tuple()
    major, minor = int(major), int(minor)
    
    if major < 3 or (major == 3 and minor < 8):
        logger.error(f"Python版本不满足要求: 当前版本 {platform.python_version()}, 需要 Python 3.8+")
        return False
    
    logger.info(f"Python版本检查通过: {platform.python_version()}")
    return True

def check_pip():
    """检查pip是否可用"""
    logger.info("检查pip...")
    try:
        subprocess.run(
            [sys.executable, "-m", "pip", "--version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            check=True
        )
        logger.info("pip检查通过")
        return True
    except (subprocess.SubprocessError, FileNotFoundError):
        logger.error("pip不可用，请安装pip")
        return False

def install_package(package_name):
    """安装Python包"""
    logger.info(f"安装包: {package_name}")
    try:
        subprocess.run(
            [sys.executable, "-m", "pip", "install", "-i", TSINGHUA_MIRROR, package_name],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            check=True
        )
        logger.info(f"成功安装: {package_name}")
        return True
    except subprocess.SubprocessError as e:
        logger.error(f"安装失败: {package_name}, 错误: {str(e)}")
        return False

def check_and_install_packages():
    """检查并安装所需的Python包"""
    logger.info("检查所需的Python包...")
    
    # 获取已安装的包
    try:
        installed_packages = subprocess.run(
            [sys.executable, "-m", "pip", "list", "--format=json"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            check=True,
            universal_newlines=True
        )
        packages_json = json.loads(installed_packages.stdout)
        installed = {pkg["name"].lower(): pkg["version"] for pkg in packages_json}
    except Exception as e:
        logger.error(f"获取已安装包列表失败: {str(e)}")
        installed = {}
    
    # 检查并安装缺失的包
    all_installed = True
    for package_spec in REQUIRED_PACKAGES:
        # 解析包名和版本要求
        match = re.match(r"([a-zA-Z0-9_-]+)(?:[><=]=?([0-9.]+))?", package_spec)
        if not match:
            logger.warning(f"无法解析包规格: {package_spec}")
            continue
            
        package_name, version_req = match.groups()
        package_name_lower = package_name.lower()
        
        # 检查包是否已安装
        if package_name_lower not in installed:
            logger.warning(f"缺少包: {package_name}")
            if not install_package(package_spec):
                all_installed = False
        else:
            logger.info(f"已安装: {package_name} {installed[package_name_lower]}")
    
    return all_installed

def check_ffmpeg():
    """检查FFmpeg是否已安装"""
    logger.info("检查FFmpeg...")
    
    # 尝试在系统路径中查找ffmpeg
    try:
        process = subprocess.run(
            ["ffmpeg", "-version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            check=False
        )
        if process.returncode == 0:
            logger.info("FFmpeg已安装在系统路径中")
            return True
    except FileNotFoundError:
        pass
    
    # 检查常见的安装位置
    common_paths = [
        r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
        r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe",
        r"C:\ffmpeg\bin\ffmpeg.exe",
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "ffmpeg.exe")
    ]
    
    for path in common_paths:
        if os.path.exists(path):
            logger.info(f"FFmpeg已安装: {path}")
            return True
    
    logger.warning("FFmpeg未安装")
    return False

def download_and_install_ffmpeg():
    """下载并安装FFmpeg"""
    logger.info("开始下载并安装FFmpeg...")
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    logger.info(f"创建临时目录: {temp_dir}")
    
    try:
        # 下载FFmpeg
        zip_path = os.path.join(temp_dir, "ffmpeg.zip")
        logger.info("从清华镜像下载FFmpeg...")
        
        try:
            # 使用清华镜像下载
            subprocess.run(
                ["curl", "-L", FFMPEG_MIRROR_URL, "-o", zip_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )
        except subprocess.SubprocessError:
            # 如果清华镜像下载失败，尝试从GitHub下载
            logger.warning("从清华镜像下载失败，尝试从GitHub下载...")
            subprocess.run(
                ["curl", "-L", FFMPEG_DOWNLOAD_URL, "-o", zip_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=True
            )
        
        # 解压FFmpeg
        logger.info("解压FFmpeg...")
        import zipfile
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        
        # 查找ffmpeg.exe
        ffmpeg_exe = None
        for root, dirs, files in os.walk(temp_dir):
            if "ffmpeg.exe" in files:
                ffmpeg_exe = os.path.join(root, "ffmpeg.exe")
                break
        
        if ffmpeg_exe:
            # 复制到当前目录
            dest_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ffmpeg.exe")
            shutil.copy2(ffmpeg_exe, dest_path)
            logger.info(f"FFmpeg已安装到: {dest_path}")
            return True
        else:
            logger.error("在下载的文件中未找到ffmpeg.exe")
            return False
            
    except Exception as e:
        logger.error(f"下载安装FFmpeg失败: {str(e)}")
        return False
    finally:
        # 清理临时目录
        try:
            shutil.rmtree(temp_dir)
        except Exception as e:
            logger.warning(f"清理临时目录失败: {str(e)}")

def update_config_file():
    """更新配置文件中的FFmpeg路径"""
    logger.info("更新配置文件...")
    
    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.py")
    if not os.path.exists(config_path):
        logger.warning(f"配置文件不存在: {config_path}")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找FFmpeg路径配置
        ffmpeg_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ffmpeg.exe")
        if os.path.exists(ffmpeg_path):
            # 更新FFmpeg路径
            pattern = r'("ffmpeg_path"\s*:\s*)"[^"]*"'
            replacement = f'\\1"{ffmpeg_path.replace("\\", "\\\\")}"'
            content = re.sub(pattern, replacement, content)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(content)
                
            logger.info(f"已更新配置文件中的FFmpeg路径: {ffmpeg_path}")
            return True
        else:
            logger.warning("FFmpeg不存在，无法更新配置文件")
            return False
    except Exception as e:
        logger.error(f"更新配置文件失败: {str(e)}")
        return False

def start_application():
    """启动主应用程序"""
    logger.info("启动录屏软件...")
    
    main_script = os.path.join(os.path.dirname(os.path.abspath(__file__)), "main.py")
    if not os.path.exists(main_script):
        logger.error(f"主程序脚本不存在: {main_script}")
        return False
    
    try:
        subprocess.Popen(
            [sys.executable, main_script],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        logger.info("录屏软件已启动")
        return True
    except Exception as e:
        logger.error(f"启动录屏软件失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("=== 录屏软件一键启动器 ===")
    
    # 检查Python版本
    if not check_python_version():
        input("按Enter键退出...")
        return
    
    # 检查pip
    if not check_pip():
        input("按Enter键退出...")
        return
    
    # 检查并安装所需的Python包
    if not check_and_install_packages():
        logger.warning("部分Python包安装失败，程序可能无法正常运行")
    
    # 检查FFmpeg
    if not check_ffmpeg():
        logger.info("FFmpeg未安装，正在下载并安装...")
        if download_and_install_ffmpeg():
            # 更新配置文件
            update_config_file()
        else:
            logger.warning("FFmpeg安装失败，程序可能无法正常合并音视频")
    
    # 启动应用程序
    start_application()

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logger.error(f"启动器发生错误: {str(e)}")
        input("按Enter键退出...")
