@echo off
echo 正在启动录屏软件...
echo 请稍候，正在检查环境并安装必要的依赖...

:: 检查Python是否已安装
where python >nul 2>nul
if %errorlevel% neq 0 (
    echo Python未安装，请先安装Python 3.8或更高版本
    echo 您可以从 https://www.python.org/downloads/ 下载安装
    pause
    exit /b
)

:: 如果图标不存在，创建图标
if not exist "%~dp0recorder_icon.ico" (
    echo 创建应用图标...
    python "%~dp0create_icon.py"
)

:: 启动启动器
echo 正在启动程序...
python "%~dp0launcher.py"

:: 如果启动器出错，等待用户按键
if %errorlevel% neq 0 (
    echo 启动器出错，请查看上面的错误信息
    pause
)

exit /b
