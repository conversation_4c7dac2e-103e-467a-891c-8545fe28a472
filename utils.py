"""
工具函数模块 - 提供各种辅助功能
"""

import os
import sys
import re
import logging
import tempfile
import subprocess
import platform
from datetime import datetime
import config

# 设置日志
def setup_logging():
    """设置日志系统"""
    log_config = config.LOG_CONFIG
    log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 创建日志目录
    os.makedirs(os.path.dirname(log_config["log_file"]), exist_ok=True)

    # 文件处理器
    file_handler = logging.handlers.RotatingFileHandler(
        log_config["log_file"],
        maxBytes=log_config["max_log_size"],
        backupCount=log_config["backup_count"]
    )
    file_handler.setFormatter(log_formatter)

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(log_formatter)

    # 根日志配置
    root_logger = logging.getLogger()
    root_logger.setLevel(log_config["log_level"])
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

    return root_logger

# 创建日志记录器
logger = setup_logging()

def get_temp_file_path(prefix="temp", suffix=".mp4"):
    """获取临时文件路径"""
    temp_dir = config.DEFAULT_TEMP_DIR
    os.makedirs(temp_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return os.path.join(temp_dir, f"{prefix}_{timestamp}{suffix}")

def get_output_file_path(prefix="recording", extension="mp4"):
    """获取输出文件路径"""
    output_dir = config.DEFAULT_OUTPUT_DIR
    os.makedirs(output_dir, exist_ok=True)
    filename = config.get_default_filename(prefix, extension)
    return os.path.join(output_dir, filename)

def check_ffmpeg_installed():
    """检查FFmpeg是否已安装"""
    try:
        # 尝试使用配置中的路径
        ffmpeg_path = config.FORMAT_CONVERSION_CONFIG["ffmpeg_path"]
        result = subprocess.run(
            [ffmpeg_path, "-version"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            check=False
        )

        if result.returncode == 0:
            logger.info(f"FFmpeg已安装: {ffmpeg_path}")
            return True

        # 如果配置的路径不可用，尝试备用路径
        logger.warning(f"配置的FFmpeg路径不可用: {ffmpeg_path}，尝试备用路径")

        # 获取备用路径列表
        alt_paths = config.FORMAT_CONVERSION_CONFIG.get("ffmpeg_alt_paths", [])

        # 尝试每个备用路径
        for path in alt_paths:
            if os.path.exists(path):
                try:
                    result = subprocess.run(
                        [path, "-version"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        check=False
                    )

                    if result.returncode == 0:
                        # 更新配置
                        config.FORMAT_CONVERSION_CONFIG["ffmpeg_path"] = path
                        logger.info(f"找到可用的FFmpeg: {path}")
                        return True
                except Exception as e:
                    logger.warning(f"尝试FFmpeg路径 {path} 时出错: {str(e)}")
                    continue

        # 尝试在系统PATH中查找
        try:
            # 在Windows上使用where命令查找ffmpeg
            where_result = subprocess.run(
                ["where", "ffmpeg"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=False
            )

            if where_result.returncode == 0:
                # 获取第一个找到的路径
                system_path = where_result.stdout.decode('utf-8').strip().split('\n')[0]

                if os.path.exists(system_path):
                    # 验证路径
                    test_result = subprocess.run(
                        [system_path, "-version"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        check=False
                    )

                    if test_result.returncode == 0:
                        # 更新配置
                        config.FORMAT_CONVERSION_CONFIG["ffmpeg_path"] = system_path
                        logger.info(f"在系统PATH中找到FFmpeg: {system_path}")
                        return True
        except Exception as e:
            logger.warning(f"在系统PATH中查找FFmpeg时出错: {str(e)}")

        # 如果所有尝试都失败，记录错误并返回False
        logger.error("FFmpeg未安装或所有已知路径都不可用")
        return False
    except Exception as e:
        logger.error(f"检查FFmpeg时发生错误: {str(e)}")
        return False

def get_screen_resolution():
    """获取屏幕分辨率"""
    import pyautogui
    width, height = pyautogui.size()
    return width, height

def get_available_audio_devices():
    """获取可用的音频设备"""
    import pyaudio
    p = pyaudio.PyAudio()
    devices = []

    for i in range(p.get_device_count()):
        device_info = p.get_device_info_by_index(i)
        devices.append({
            'index': i,
            'name': device_info['name'],
            'max_input_channels': device_info['maxInputChannels'],
            'max_output_channels': device_info['maxOutputChannels'],
            'default_sample_rate': device_info['defaultSampleRate']
        })

    p.terminate()
    return devices

def format_time(seconds):
    """将秒数格式化为时:分:秒格式"""
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"

def is_admin():
    """检查程序是否以管理员权限运行"""
    try:
        if platform.system() == 'Windows':
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        else:
            return os.geteuid() == 0
    except:
        return False

def resource_path(relative_path):
    """获取资源文件的绝对路径（用于PyInstaller打包）"""
    try:
        # PyInstaller创建临时文件夹并将路径存储在_MEIPASS中
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")

    return os.path.join(base_path, relative_path)

def ensure_dir_exists(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logger.info(f"创建目录: {directory}")
    return directory

def get_system_info():
    """获取系统信息"""
    return {
        "os": platform.system(),
        "os_version": platform.version(),
        "architecture": platform.architecture(),
        "processor": platform.processor(),
        "python_version": platform.python_version(),
    }

def get_media_duration(file_path, ffmpeg_path=None):
    """
    获取媒体文件的时长（秒）

    参数:
        file_path (str): 媒体文件路径
        ffmpeg_path (str): FFmpeg可执行文件路径，如果为None则使用配置中的路径

    返回:
        float: 媒体文件时长（秒），如果失败则返回None
    """
    try:
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return None

        if ffmpeg_path is None:
            ffmpeg_path = config.FORMAT_CONVERSION_CONFIG["ffmpeg_path"]

        # 使用FFmpeg获取媒体文件信息
        command = [
            ffmpeg_path,
            "-i", file_path,
            "-hide_banner"
        ]

        process = subprocess.run(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )

        # FFmpeg将信息输出到stderr
        output = process.stderr

        # 查找时长信息
        duration_match = re.search(r"Duration: (\d{2}):(\d{2}):(\d{2})\.(\d{2})", output)
        if duration_match:
            hours = int(duration_match.group(1))
            minutes = int(duration_match.group(2))
            seconds = int(duration_match.group(3))
            milliseconds = int(duration_match.group(4))

            # 计算总秒数
            total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 100
            logger.debug(f"媒体文件时长: {file_path} = {total_seconds}秒")
            return total_seconds
        else:
            logger.warning(f"无法获取媒体文件时长: {file_path}")
            return None

    except Exception as e:
        logger.error(f"获取媒体文件时长时发生错误: {str(e)}")
        return None

def download_ffmpeg():
    """
    下载并安装FFmpeg

    返回:
        bool: 是否成功下载并安装FFmpeg
    """
    try:
        import webbrowser
        import tempfile
        import zipfile
        import shutil

        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        logger.info(f"创建临时目录: {temp_dir}")

        # 打开FFmpeg下载页面
        webbrowser.open("https://ffmpeg.org/download.html")

        # 显示安装指南
        print("=== FFmpeg安装指南 ===")
        print("1. 从打开的网页下载适合您系统的FFmpeg")
        print("2. 解压下载的文件")
        print("3. 将解压后的bin目录添加到系统PATH环境变量")
        print("4. 重启应用程序")
        print("或者，您可以将ffmpeg.exe文件复制到应用程序目录")

        return True
    except Exception as e:
        logger.error(f"下载FFmpeg时发生错误: {str(e)}")
        return False
