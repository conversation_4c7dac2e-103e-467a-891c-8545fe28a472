"""
配置文件 - 存储录屏软件的全局配置
"""

import os
from datetime import datetime

# 基本配置
DEFAULT_OUTPUT_DIR = os.path.join(os.path.expanduser("~"), "Videos", "ScreenRecorder")
DEFAULT_TEMP_DIR = os.path.join(os.path.expanduser("~"), "AppData", "Local", "Temp", "ScreenRecorder")

# 确保目录存在
os.makedirs(DEFAULT_OUTPUT_DIR, exist_ok=True)
os.makedirs(DEFAULT_TEMP_DIR, exist_ok=True)

# 录制配置
SCREEN_RECORD_CONFIG = {
    "fps": 30,                      # 帧率
    "quality": 80,                  # 视频质量 (1-100)
    "codec": "XVID",                # 视频编码器
    "output_format": "mp4",         # 默认输出格式
    "record_audio": True,           # 是否录制音频
    "record_system_audio": True,    # 是否录制系统音频
    "record_microphone": False,     # 是否录制麦克风
    "audio_quality": 44100,         # 音频采样率
    "audio_channels": 2,            # 音频通道数
}

# 隐藏模式配置
HIDDEN_MODE_CONFIG = {
    "enabled": False,               # 是否启用隐藏模式
    "hotkey_start": "ctrl+shift+f9", # 开始录制快捷键
    "hotkey_stop": "ctrl+shift+f10", # 停止录制快捷键
    "hide_from_taskbar": True,      # 是否从任务栏隐藏
    "minimize_to_tray": True,       # 是否最小化到系统托盘
    "password_protected": False,    # 是否启用密码保护
    "password": "",                 # 密码（如果启用）
}

# 格式转换配置
FORMAT_CONVERSION_CONFIG = {
    "supported_formats": ["mp4", "avi", "mkv", "wmv", "mov", "flv", "webm"],
    "default_format": "mp4",
    "ffmpeg_path": "ffmpeg",        # FFmpeg可执行文件路径
    "ffmpeg_alt_paths": [           # FFmpeg备用路径列表
        r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
        r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe",
        r"C:\ffmpeg\bin\ffmpeg.exe",
        r"ffmpeg.exe"
    ],
    "use_hardware_acceleration": True, # 是否使用硬件加速
    "preset": "medium",             # 编码预设 (ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow)
}

# GUI配置
GUI_CONFIG = {
    "theme": "light",               # 主题 (light, dark)
    "language": "zh_CN",            # 语言
    "show_tooltips": True,          # 是否显示工具提示
    "minimize_to_tray_on_close": True, # 关闭时最小化到托盘
    "start_minimized": False,       # 启动时最小化
    "always_on_top": False,         # 窗口是否总在最前
}

# 快捷键配置
HOTKEYS = {
    "start_recording": "f9",        # 开始录制
    "pause_recording": "f10",       # 暂停录制
    "stop_recording": "f11",        # 停止录制
    "screenshot": "f12",            # 截图
}

# 文件命名格式
def get_default_filename(prefix="recording", extension="mp4"):
    """生成默认文件名"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{prefix}_{timestamp}.{extension}"

# 日志配置
LOG_CONFIG = {
    "log_level": "INFO",            # 日志级别
    "log_file": os.path.join(DEFAULT_TEMP_DIR, "screen_recorder.log"), # 日志文件
    "max_log_size": 10 * 1024 * 1024, # 最大日志大小 (10MB)
    "backup_count": 3,              # 备份日志文件数量
}
