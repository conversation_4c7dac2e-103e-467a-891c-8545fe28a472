('D:\\AI_project\\录屏软件\\video_python\\build\\强大的录屏软件\\强大的录屏软件.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\AI_project\\录屏软件\\video_python\\build\\强大的录屏软件\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\AI_project\\录屏软件\\video_python\\build\\强大的录屏软件\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\AI_project\\录屏软件\\video_python\\build\\强大的录屏软件\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\AI_project\\录屏软件\\video_python\\build\\强大的录屏软件\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\AI_project\\录屏软件\\video_python\\build\\强大的录屏软件\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\AI_project\\录屏软件\\video_python\\build\\强大的录屏软件\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('main', 'D:\\AI_project\\录屏软件\\video_python\\main.py', 'PYSOURCE'),
  ('python312.dll', 'C:\\ProgramData\\anaconda3\\python312.dll', 'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\ProgramData\\anaconda3\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\ProgramData\\anaconda3\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'C:\\ProgramData\\anaconda3\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\ProgramData\\anaconda3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\ProgramData\\anaconda3\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\ProgramData\\anaconda3\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\ProgramData\\anaconda3\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_wmi.pyd', 'C:\\ProgramData\\anaconda3\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\ProgramData\\anaconda3\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\ProgramData\\anaconda3\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\ProgramData\\anaconda3\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\ProgramData\\anaconda3\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\ProgramData\\anaconda3\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\ProgramData\\anaconda3\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\ProgramData\\anaconda3\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('unicodedata2.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\unicodedata2.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy\\random\\mtrand.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy\\random\\_sfc64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy\\random\\_philox.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy\\random\\_pcg64.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy\\random\\_mt19937.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy\\random\\bit_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy\\random\\_generator.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy\\random\\_common.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\yaml\\_yaml.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('pyaudio\\_portaudio.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\pyaudio\\_portaudio.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\ProgramData\\anaconda3\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('PIL\\_imaging.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PIL\\_imaging.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PIL\\_webp.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PIL\\_imagingtk.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PIL\\_imagingcms.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PIL\\_imagingmath.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\_cffi_backend.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\ProgramData\\anaconda3\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PIL\\_imagingft.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'C:\\ProgramData\\anaconda3\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp312-win_amd64.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\cv2\\cv2.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\ProgramData\\anaconda3\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'C:\\ProgramData\\anaconda3\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\ProgramData\\anaconda3\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('MSVCP140.dll', 'C:\\ProgramData\\anaconda3\\MSVCP140.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'C:\\ProgramData\\anaconda3\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('liblzma.dll',
   'C:\\ProgramData\\anaconda3\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'C:\\ProgramData\\anaconda3\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('ffi-8.dll',
   'C:\\ProgramData\\anaconda3\\Library\\bin\\ffi-8.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'C:\\ProgramData\\anaconda3\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('libexpat.dll',
   'C:\\ProgramData\\anaconda3\\Library\\bin\\libexpat.dll',
   'BINARY'),
  ('python3.dll', 'C:\\ProgramData\\anaconda3\\python3.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes312.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\win32\\pywintypes312.dll',
   'BINARY'),
  ('yaml.dll', 'C:\\ProgramData\\anaconda3\\Library\\bin\\yaml.dll', 'BINARY'),
  ('tk86t.dll',
   'C:\\ProgramData\\anaconda3\\Library\\bin\\tk86t.dll',
   'BINARY'),
  ('tcl86t.dll',
   'C:\\ProgramData\\anaconda3\\Library\\bin\\tcl86t.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\ProgramData\\anaconda3\\ucrtbase.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\ProgramData\\anaconda3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('recorder_icon.ico',
   'D:\\AI_project\\录屏软件\\video_python\\recorder_icon.ico',
   'DATA'),
  ('base_library.zip',
   'D:\\AI_project\\录屏软件\\video_python\\build\\强大的录屏软件\\base_library.zip',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\LICENSE',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\WHEEL',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\INSTALLER',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\METADATA',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\direct_url.json',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\direct_url.json',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\top_level.txt',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\RECORD',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-7.0.1.dist-info\\REQUESTED',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\importlib_metadata-7.0.1.dist-info\\REQUESTED',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tk_data\\text.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tk_data\\console.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'C:/ProgramData/anaconda3/Library/lib\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.7.tm',
   'C:/ProgramData/anaconda3/Library/lib\\tcl8\\8.5\\tcltest-2.5.7.tm',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:/ProgramData/anaconda3/Library/lib\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'C:/ProgramData/anaconda3/Library/lib\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:/ProgramData/anaconda3/Library/lib\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tk_data\\tclIndex',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tk_data\\button.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\button.tcl',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tk_data\\license.terms',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'C:/ProgramData/anaconda3/Library/lib\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:/ProgramData/anaconda3/Library/lib/tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('cv2\\config-3.py',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('cv2\\config.py',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\cv2\\config.py',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('numpy-2.2.6.dist-info\\REQUESTED',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy-2.2.6.dist-info\\REQUESTED',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.43.0.dist-info\\entry_points.txt',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('wheel-0.43.0.dist-info\\WHEEL',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.43.0.dist-info\\LICENSE.txt',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.43.0.dist-info\\RECORD',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.43.0.dist-info\\METADATA',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('cv2\\__init__.py',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'C:\\ProgramData\\anaconda3\\Lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
