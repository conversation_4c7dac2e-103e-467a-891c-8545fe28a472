#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
存储管理模块 - 管理录制文件的存储
"""

import os
import logging
import shutil
import time
from datetime import datetime, timedelta
from PyQt5.QtCore import QObject, QTimer, pyqtSignal

import config

# 设置日志
logger = logging.getLogger(__name__)

class StorageManager(QObject):
    """存储管理类"""
    
    # 定义信号
    storage_warning = pyqtSignal(str)  # 存储警告信号
    cleanup_completed = pyqtSignal(int, float)  # 清理完成信号（删除文件数，释放空间GB）
    
    def __init__(self, parent=None):
        """初始化存储管理器"""
        super().__init__(parent)
        self.parent = parent
        
        # 从设置加载配置
        self.max_storage_time = 24  # 默认24小时
        self.max_folder_size = 30   # 默认30GB
        self.disk_limit = 5         # 默认5GB警戒线
        
        # 创建定时器，定期检查存储状态
        self.check_timer = QTimer(self)
        self.check_timer.timeout.connect(self.check_storage)
        self.check_timer.start(60 * 60 * 1000)  # 每小时检查一次
        
        # 立即进行一次检查
        QTimer.singleShot(5000, self.check_storage)
        
    def update_settings(self, max_time=None, max_size=None, disk_limit=None):
        """更新设置"""
        if max_time is not None:
            self.max_storage_time = max_time
        if max_size is not None:
            self.max_folder_size = max_size
        if disk_limit is not None:
            self.disk_limit = disk_limit
            
        logger.info(f"存储管理器设置已更新: 最大时间={self.max_storage_time}小时, "
                   f"最大容量={self.max_folder_size}GB, 警戒线={self.disk_limit}GB")
        
    def check_storage(self):
        """检查存储状态"""
        try:
            output_dir = config.DEFAULT_OUTPUT_DIR
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
                return
                
            # 检查硬盘剩余空间
            disk_usage = shutil.disk_usage(output_dir)
            free_gb = disk_usage.free / (1024 ** 3)  # 转换为GB
            
            # 如果低于警戒线，发出警告
            if free_gb < self.disk_limit:
                warning_msg = f"硬盘剩余空间不足! 仅剩 {free_gb:.2f}GB，低于警戒线 {self.disk_limit}GB"
                logger.warning(warning_msg)
                self.storage_warning.emit(warning_msg)
                
                # 尝试清理旧文件
                self.cleanup_old_files()
                
            # 检查文件夹总大小
            folder_size_gb = self.get_folder_size(output_dir) / (1024 ** 3)
            if folder_size_gb > self.max_folder_size:
                logger.info(f"录制文件夹大小({folder_size_gb:.2f}GB)超过限制({self.max_folder_size}GB)，开始清理...")
                self.cleanup_old_files()
                
        except Exception as e:
            logger.error(f"检查存储状态失败: {str(e)}")
            
    def get_folder_size(self, folder_path):
        """获取文件夹大小（字节）"""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(folder_path):
            for filename in filenames:
                file_path = os.path.join(dirpath, filename)
                if os.path.isfile(file_path):
                    total_size += os.path.getsize(file_path)
        return total_size
        
    def cleanup_old_files(self):
        """清理旧文件"""
        try:
            output_dir = config.DEFAULT_OUTPUT_DIR
            if not os.path.exists(output_dir):
                return
                
            # 获取所有录制文件
            video_files = []
            for root, dirs, files in os.walk(output_dir):
                for file in files:
                    if file.endswith((".mp4", ".avi", ".mkv", ".mov", ".flv", ".wmv")):
                        file_path = os.path.join(root, file)
                        # 获取文件修改时间
                        mod_time = os.path.getmtime(file_path)
                        file_size = os.path.getsize(file_path)
                        video_files.append((file_path, mod_time, file_size))
            
            # 按修改时间排序（最旧的在前）
            video_files.sort(key=lambda x: x[1])
            
            # 计算时间阈值
            time_threshold = time.time() - (self.max_storage_time * 3600)
            
            # 删除过期文件
            deleted_count = 0
            freed_space = 0
            
            for file_path, mod_time, file_size in video_files:
                # 如果文件超过最大存储时间，删除它
                if mod_time < time_threshold:
                    try:
                        os.remove(file_path)
                        logger.info(f"已删除过期文件: {file_path}")
                        deleted_count += 1
                        freed_space += file_size
                    except Exception as e:
                        logger.error(f"删除文件失败: {file_path}, 错误: {str(e)}")
                        
            # 如果仍然超过最大文件夹大小，继续删除最旧的文件
            if deleted_count > 0:
                # 重新检查文件夹大小
                folder_size_gb = self.get_folder_size(output_dir) / (1024 ** 3)
                
                if folder_size_gb > self.max_folder_size:
                    # 重新获取文件列表
                    video_files = []
                    for root, dirs, files in os.walk(output_dir):
                        for file in files:
                            if file.endswith((".mp4", ".avi", ".mkv", ".mov", ".flv", ".wmv")):
                                file_path = os.path.join(root, file)
                                mod_time = os.path.getmtime(file_path)
                                file_size = os.path.getsize(file_path)
                                video_files.append((file_path, mod_time, file_size))
                    
                    # 按修改时间排序
                    video_files.sort(key=lambda x: x[1])
                    
                    # 继续删除最旧的文件，直到文件夹大小低于限制
                    for file_path, mod_time, file_size in video_files:
                        try:
                            os.remove(file_path)
                            logger.info(f"已删除旧文件以释放空间: {file_path}")
                            deleted_count += 1
                            freed_space += file_size
                            
                            # 重新检查文件夹大小
                            folder_size_gb = self.get_folder_size(output_dir) / (1024 ** 3)
                            if folder_size_gb <= self.max_folder_size:
                                break
                        except Exception as e:
                            logger.error(f"删除文件失败: {file_path}, 错误: {str(e)}")
            
            # 发送清理完成信号
            if deleted_count > 0:
                freed_space_gb = freed_space / (1024 ** 3)
                logger.info(f"存储清理完成: 删除了 {deleted_count} 个文件，释放了 {freed_space_gb:.2f}GB 空间")
                self.cleanup_completed.emit(deleted_count, freed_space_gb)
                
        except Exception as e:
            logger.error(f"清理旧文件失败: {str(e)}")
            
    def split_recording_if_needed(self, current_file, recording_start_time):
        """检查是否需要分割录制文件"""
        if not current_file or not recording_start_time:
            return False
            
        # 计算当前录制时长（分钟）
        elapsed_minutes = (datetime.now() - recording_start_time).total_seconds() / 60
        
        # 如果超过单文件时长限制，返回True表示需要分割
        return elapsed_minutes >= self.max_storage_time
