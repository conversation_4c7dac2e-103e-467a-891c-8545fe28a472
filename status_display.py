#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
状态显示模块 - 提供录屏状态显示功能
"""

import os
import logging
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QLabel, QVBoxLayout, QHBoxLayout, 
                            QGroupBox, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette

# 设置日志
logger = logging.getLogger(__name__)

class StatusDisplay(QWidget):
    """录屏状态显示组件"""
    
    # 定义信号
    status_updated = pyqtSignal(str, str, str)  # 状态、文件名、时长
    
    def __init__(self, parent=None):
        """初始化状态显示组件"""
        super().__init__(parent)
        self.parent = parent
        self.recording = False
        self.start_time = None
        self.current_file = "/"
        self.elapsed_time = timedelta()
        self.paused = False
        self.pause_time = None
        
        self.init_ui()
        
        # 创建定时器，用于更新录制时长
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_elapsed_time)
        self.timer.start(100)  # 每100毫秒更新一次
        
    def init_ui(self):
        """初始化界面"""
        # 主布局
        main_layout = QVBoxLayout()
        
        # 状态组
        status_group = QGroupBox("录屏状态")
        status_layout = QVBoxLayout()
        
        # 录制状态
        self.status_label = QLabel("未开始录制")
        self.status_label.setFont(QFont("Arial", 10, QFont.Bold))
        self.status_label.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(self.status_label)
        
        # 文件名
        file_layout = QHBoxLayout()
        file_layout.addWidget(QLabel("当前文件名:"))
        self.file_label = QLabel("/")
        file_layout.addWidget(self.file_label)
        status_layout.addLayout(file_layout)
        
        # 录制时长
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("当前文件录屏时长:"))
        self.time_label = QLabel("00:00:00.000")
        time_layout.addWidget(self.time_label)
        status_layout.addLayout(time_layout)
        
        status_group.setLayout(status_layout)
        main_layout.addWidget(status_group)
        
        self.setLayout(main_layout)
        
    def start_recording(self, file_path):
        """开始录制"""
        self.recording = True
        self.paused = False
        self.start_time = datetime.now()
        self.current_file = file_path
        self.elapsed_time = timedelta()
        
        # 更新UI
        self.status_label.setText("正在录制")
        self.status_label.setStyleSheet("color: red;")
        self.file_label.setText(os.path.basename(file_path))
        
        # 发送信号
        self.status_updated.emit("recording", os.path.basename(file_path), "00:00:00.000")
        
        logger.info(f"状态显示: 开始录制 {file_path}")
        
    def pause_recording(self):
        """暂停录制"""
        if self.recording and not self.paused:
            self.paused = True
            self.pause_time = datetime.now()
            
            # 计算已经录制的时间
            self.elapsed_time += self.pause_time - self.start_time
            
            # 更新UI
            self.status_label.setText("录制已暂停")
            self.status_label.setStyleSheet("color: orange;")
            
            # 发送信号
            self.status_updated.emit("paused", os.path.basename(self.current_file), 
                                    self.format_timedelta(self.elapsed_time))
            
            logger.info("状态显示: 暂停录制")
            
    def resume_recording(self):
        """恢复录制"""
        if self.recording and self.paused:
            self.paused = False
            self.start_time = datetime.now()
            
            # 更新UI
            self.status_label.setText("正在录制")
            self.status_label.setStyleSheet("color: red;")
            
            # 发送信号
            self.status_updated.emit("recording", os.path.basename(self.current_file), 
                                    self.format_timedelta(self.elapsed_time))
            
            logger.info("状态显示: 恢复录制")
            
    def stop_recording(self):
        """停止录制"""
        if self.recording:
            self.recording = False
            self.paused = False
            
            # 计算总录制时间
            if self.paused:
                total_time = self.elapsed_time
            else:
                end_time = datetime.now()
                total_time = self.elapsed_time + (end_time - self.start_time)
            
            # 更新UI
            self.status_label.setText("未开始录制")
            self.status_label.setStyleSheet("")
            self.time_label.setText(self.format_timedelta(total_time))
            
            # 发送信号
            self.status_updated.emit("stopped", os.path.basename(self.current_file), 
                                    self.format_timedelta(total_time))
            
            logger.info(f"状态显示: 停止录制，总时长: {self.format_timedelta(total_time)}")
            
    def update_elapsed_time(self):
        """更新录制时长"""
        if self.recording and not self.paused:
            current_time = datetime.now()
            # 计算本次录制的时间差（避免时间漂移）
            current_elapsed = current_time - self.start_time
            # 总时长 = 暂停前累计时长 + 本次录制时长
            total_elapsed = self.elapsed_time + current_elapsed
            # 更新显示（限制毫秒为三位提高可读性）
            self.time_label.setText(self.format_timedelta(total_elapsed))
            # 添加调试日志（生产环境可移除）
            logger.debug(f"更新录制时长: 累计={total_elapsed}, 本次={current_elapsed}")
            
    def format_timedelta(self, td):
        """格式化时间差为字符串（匹配UI三位毫秒显示）"""
        # 获取总的毫秒数（取三位有效数字）
        total_seconds = td.total_seconds()
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = int(total_seconds % 60)
        milliseconds = int((total_seconds % 1) * 1000)  # 改为三位毫秒
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}.{milliseconds:03d}"
        
    def get_recording_status(self):
        """获取录制状态"""
        if self.recording:
            if self.paused:
                return "paused"
            else:
                return "recording"
        else:
            return "stopped"
            
    def get_current_file(self):
        """获取当前文件名"""
        return os.path.basename(self.current_file)
        
    def get_elapsed_time(self):
        """获取已录制时长"""
        if self.recording:
            if self.paused:
                return self.format_timedelta(self.elapsed_time)
            else:
                current_time = datetime.now()
                elapsed = self.elapsed_time + (current_time - self.start_time)
                return self.format_timedelta(elapsed)
        else:
            return self.format_timedelta(self.elapsed_time)
