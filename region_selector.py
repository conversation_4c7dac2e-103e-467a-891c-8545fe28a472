"""
区域选择模块 - 提供屏幕区域选择功能
"""

import os
import sys
import logging
from PyQt5.QtWidgets import (
    QWidget, QApplication, QLabel, QPushButton, QVBoxLayout, QHBoxLayout,
    QDialog, QDesktopWidget
)
from PyQt5.QtCore import Qt, QRect, QPoint, pyqtSignal
from PyQt5.QtGui import QColor, QPen, QPainter, QGuiApplication, QCursor, QPixmap

logger = logging.getLogger(__name__)

class RegionSelector(QDialog):
    """屏幕区域选择器"""
    
    region_selected = pyqtSignal(tuple)  # 信号：(left, top, width, height)
    
    def __init__(self, parent=None):
        """初始化区域选择器"""
        super().__init__(parent, Qt.FramelessWindowHint)
        
        # 设置窗口属性
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setWindowState(Qt.WindowFullScreen)
        
        # 获取屏幕尺寸
        self.screen_geometry = QDesktopWidget().screenGeometry()
        self.setGeometry(self.screen_geometry)
        
        # 截图整个屏幕
        self.screen_pixmap = QGuiApplication.primaryScreen().grabWindow(0)
        
        # 选择区域
        self.selection_rect = QRect()
        self.start_point = QPoint()
        self.end_point = QPoint()
        self.is_selecting = False
        
        # 设置鼠标追踪
        self.setMouseTracking(True)
        
        # 创建UI
        self.setup_ui()
        
        logger.info("初始化区域选择器")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 提示标签
        self.hint_label = QLabel("按住鼠标左键拖动选择区域，按ESC取消", self)
        self.hint_label.setAlignment(Qt.AlignCenter)
        self.hint_label.setStyleSheet("""
            background-color: rgba(0, 0, 0, 150);
            color: white;
            padding: 10px;
            border-radius: 5px;
        """)
        self.hint_label.setFixedHeight(40)
        self.hint_label.move(
            (self.screen_geometry.width() - self.hint_label.width()) // 2,
            10
        )
        
        # 信息标签（显示选择区域的尺寸）
        self.info_label = QLabel(self)
        self.info_label.setAlignment(Qt.AlignCenter)
        self.info_label.setStyleSheet("""
            background-color: rgba(0, 0, 0, 150);
            color: white;
            padding: 5px;
            border-radius: 3px;
        """)
        self.info_label.hide()
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 10)
        button_layout.setSpacing(10)
        
        # 确认按钮
        self.confirm_button = QPushButton("确认", self)
        self.confirm_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.confirm_button.clicked.connect(self.confirm_selection)
        self.confirm_button.hide()
        
        # 取消按钮
        self.cancel_button = QPushButton("取消", self)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.hide()
        
        button_layout.addWidget(self.confirm_button)
        button_layout.addWidget(self.cancel_button)
        
        # 按钮容器
        self.button_container = QWidget(self)
        self.button_container.setLayout(button_layout)
        self.button_container.setFixedWidth(200)
        self.button_container.hide()
    
    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        
        # 绘制屏幕截图
        painter.drawPixmap(0, 0, self.screen_pixmap)
        
        # 绘制半透明遮罩
        painter.setBrush(QColor(0, 0, 0, 100))
        painter.setPen(Qt.NoPen)
        painter.drawRect(self.rect())
        
        # 如果有选择区域，则绘制选择区域
        if not self.selection_rect.isEmpty():
            # 清除选择区域的遮罩
            painter.setCompositionMode(QPainter.CompositionMode_Clear)
            painter.drawRect(self.selection_rect)
            
            # 绘制选择区域的边框
            painter.setCompositionMode(QPainter.CompositionMode_SourceOver)
            painter.setPen(QPen(QColor(0, 174, 255), 2, Qt.SolidLine))
            painter.setBrush(Qt.NoBrush)
            painter.drawRect(self.selection_rect)
            
            # 更新信息标签
            self.update_info_label()
            
            # 更新按钮位置
            self.update_button_position()
    
    def update_info_label(self):
        """更新信息标签"""
        if self.selection_rect.isEmpty():
            self.info_label.hide()
            return
        
        # 设置信息文本
        width = self.selection_rect.width()
        height = self.selection_rect.height()
        self.info_label.setText(f"{width} × {height}")
        self.info_label.adjustSize()
        
        # 设置信息标签位置
        x = self.selection_rect.x() + (self.selection_rect.width() - self.info_label.width()) // 2
        y = self.selection_rect.y() - self.info_label.height() - 5
        if y < 0:
            y = self.selection_rect.y() + self.selection_rect.height() + 5
        
        self.info_label.move(x, y)
        self.info_label.show()
    
    def update_button_position(self):
        """更新按钮位置"""
        if self.selection_rect.isEmpty():
            self.button_container.hide()
            return
        
        # 设置按钮容器位置
        x = self.selection_rect.x() + (self.selection_rect.width() - self.button_container.width()) // 2
        y = self.selection_rect.y() + self.selection_rect.height() + 10
        if y + self.button_container.height() > self.height():
            y = self.selection_rect.y() - self.button_container.height() - 10
        
        self.button_container.move(x, y)
        self.button_container.show()
        self.confirm_button.show()
        self.cancel_button.show()
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.is_selecting = True
            self.start_point = event.pos()
            self.end_point = event.pos()
            self.selection_rect = QRect()
            self.update()
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.is_selecting:
            self.end_point = event.pos()
            self.selection_rect = QRect(
                min(self.start_point.x(), self.end_point.x()),
                min(self.start_point.y(), self.end_point.y()),
                abs(self.start_point.x() - self.end_point.x()),
                abs(self.start_point.y() - self.end_point.y())
            )
            self.update()
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.is_selecting:
            self.is_selecting = False
            self.end_point = event.pos()
            self.selection_rect = QRect(
                min(self.start_point.x(), self.end_point.x()),
                min(self.start_point.y(), self.end_point.y()),
                abs(self.start_point.x() - self.end_point.x()),
                abs(self.start_point.y() - self.end_point.y())
            )
            self.update()
    
    def keyPressEvent(self, event):
        """键盘按下事件"""
        if event.key() == Qt.Key_Escape:
            self.reject()
    
    def confirm_selection(self):
        """确认选择"""
        if not self.selection_rect.isEmpty():
            # 发送选择区域信号
            self.region_selected.emit((
                self.selection_rect.x(),
                self.selection_rect.y(),
                self.selection_rect.width(),
                self.selection_rect.height()
            ))
            self.accept()
        else:
            self.reject()

def select_screen_region(parent=None):
    """
    选择屏幕区域
    
    参数:
        parent (QWidget): 父窗口
    
    返回:
        tuple: (left, top, width, height) 如果用户取消则返回None
    """
    selector = RegionSelector(parent)
    result = None
    
    def on_region_selected(region):
        nonlocal result
        result = region
    
    selector.region_selected.connect(on_region_selected)
    
    if selector.exec_() == QDialog.Accepted and result is not None:
        logger.info(f"已选择屏幕区域: {result}")
        return result
    else:
        logger.info("用户取消了屏幕区域选择")
        return None
