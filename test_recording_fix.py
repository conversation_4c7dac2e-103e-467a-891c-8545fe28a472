#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
录制修复测试脚本
用于测试视频录制时长与音频录制时长同步的修复效果
"""

import os
import sys
import time
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from screen_recorder import ScreenRecorder
from audio_recorder import AudioRecorder
import utils
import config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_recording_sync():
    """测试录制同步功能"""
    logger.info("开始测试录制同步功能")
    
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "test_output")
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成测试文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    video_path = os.path.join(output_dir, f"test_video_{timestamp}.mp4")
    audio_path = os.path.join(output_dir, f"test_audio_{timestamp}.wav")
    
    # 创建录制器
    screen_recorder = ScreenRecorder(
        output_path=video_path,
        region=None,  # 全屏录制
        fps=30
    )
    
    audio_recorder = AudioRecorder(
        output_path=audio_path,
        record_system_audio=True,
        record_microphone=True
    )
    
    try:
        # 开始录制
        logger.info("开始录制...")
        screen_start_success = screen_recorder.start_recording()
        audio_start_success = audio_recorder.start_recording()
        
        if not screen_start_success:
            logger.error("屏幕录制启动失败")
            return False
            
        if not audio_start_success:
            logger.error("音频录制启动失败")
            return False
        
        # 录制10秒
        test_duration = 10
        logger.info(f"录制 {test_duration} 秒...")
        
        for i in range(test_duration):
            time.sleep(1)
            screen_time = screen_recorder.get_recording_time()
            audio_time = audio_recorder.get_recording_time()
            logger.info(f"录制进度: {i+1}/{test_duration}秒 - 视频时间: {screen_time:.2f}秒, 音频时间: {audio_time:.2f}秒")
        
        # 停止录制
        logger.info("停止录制...")
        screen_recorder.stop_recording()
        audio_recorder.stop_recording()
        
        # 检查文件是否存在
        if not os.path.exists(video_path):
            logger.error(f"视频文件未生成: {video_path}")
            return False
            
        if not os.path.exists(audio_path):
            logger.error(f"音频文件未生成: {audio_path}")
            return False
        
        # 获取文件时长
        video_duration = utils.get_media_duration(video_path)
        audio_duration = utils.get_media_duration(audio_path)
        
        logger.info(f"录制完成:")
        logger.info(f"  视频文件: {video_path}")
        logger.info(f"  视频时长: {video_duration:.2f}秒")
        logger.info(f"  音频文件: {audio_path}")
        logger.info(f"  音频时长: {audio_duration:.2f}秒")
        
        # 检查时长差异
        if video_duration and audio_duration:
            time_diff = abs(video_duration - audio_duration)
            time_diff_percent = (time_diff / max(video_duration, audio_duration)) * 100
            
            logger.info(f"时长差异: {time_diff:.2f}秒 ({time_diff_percent:.1f}%)")
            
            # 如果时长差异小于10%，认为修复成功
            if time_diff_percent < 10:
                logger.info("✅ 录制同步测试通过！时长差异在可接受范围内")
                return True
            else:
                logger.warning(f"⚠️ 录制同步测试失败！时长差异过大: {time_diff_percent:.1f}%")
                return False
        else:
            logger.error("无法获取媒体文件时长信息")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False
    finally:
        # 清理资源
        try:
            if screen_recorder.is_recording:
                screen_recorder.stop_recording()
            if audio_recorder.is_recording:
                audio_recorder.stop_recording()
        except:
            pass

def test_video_fps_fix():
    """测试视频帧率修复功能"""
    logger.info("开始测试视频帧率修复功能")
    
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "test_output")
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成测试文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    video_path = os.path.join(output_dir, f"test_fps_fix_{timestamp}.mp4")
    
    # 创建录制器
    screen_recorder = ScreenRecorder(
        output_path=video_path,
        region=None,  # 全屏录制
        fps=30
    )
    
    try:
        # 开始录制
        logger.info("开始录制...")
        if not screen_recorder.start_recording():
            logger.error("屏幕录制启动失败")
            return False
        
        # 录制5秒
        test_duration = 5
        logger.info(f"录制 {test_duration} 秒...")
        time.sleep(test_duration)
        
        # 停止录制
        logger.info("停止录制...")
        screen_recorder.stop_recording()
        
        # 检查文件是否存在
        if not os.path.exists(video_path):
            logger.error(f"视频文件未生成: {video_path}")
            return False
        
        # 获取原始视频信息
        original_duration = utils.get_media_duration(video_path)
        logger.info(f"原始视频时长: {original_duration:.2f}秒")
        
        # 测试帧率修复
        logger.info("测试帧率修复...")
        fixed_path = screen_recorder.fix_video_fps(keep_original=True)
        
        if fixed_path and os.path.exists(fixed_path):
            fixed_duration = utils.get_media_duration(fixed_path)
            logger.info(f"修复后视频时长: {fixed_duration:.2f}秒")
            
            # 检查修复效果
            if fixed_duration and original_duration:
                time_diff = abs(fixed_duration - test_duration)
                if time_diff < 1.0:  # 允许1秒误差
                    logger.info("✅ 视频帧率修复测试通过！")
                    return True
                else:
                    logger.warning(f"⚠️ 视频帧率修复测试失败！时长误差: {time_diff:.2f}秒")
                    return False
            else:
                logger.error("无法获取修复后的视频时长")
                return False
        else:
            logger.error("视频帧率修复失败")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False
    finally:
        # 清理资源
        try:
            if screen_recorder.is_recording:
                screen_recorder.stop_recording()
        except:
            pass

def main():
    """主函数"""
    logger.info("开始录制修复测试")
    
    # 测试1: 录制同步
    sync_result = test_recording_sync()
    
    # 测试2: 视频帧率修复
    fps_result = test_video_fps_fix()
    
    # 输出测试结果
    logger.info("=" * 50)
    logger.info("测试结果汇总:")
    logger.info(f"  录制同步测试: {'✅ 通过' if sync_result else '❌ 失败'}")
    logger.info(f"  帧率修复测试: {'✅ 通过' if fps_result else '❌ 失败'}")
    
    if sync_result and fps_result:
        logger.info("🎉 所有测试通过！录制修复成功！")
        return True
    else:
        logger.error("❌ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
