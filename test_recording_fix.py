#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
录制修复测试脚本
用于测试视频录制时长与音频录制时长同步的修复效果
"""

import os
import sys
import time
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from screen_recorder import ScreenRecorder
from audio_recorder import AudioRecorder
import utils
import config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_recording_sync():
    """测试录制同步功能 - 重点验证帧数与时长匹配"""
    logger.info("开始测试录制同步功能")

    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "test_output")
    os.makedirs(output_dir, exist_ok=True)

    # 生成测试文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    video_path = os.path.join(output_dir, f"test_video_{timestamp}.mp4")
    audio_path = os.path.join(output_dir, f"test_audio_{timestamp}.wav")

    # 测试参数
    test_fps = 30
    test_duration = 8  # 8秒测试
    expected_frames = test_fps * test_duration  # 期望240帧

    logger.info(f"测试参数: FPS={test_fps}, 时长={test_duration}秒, 期望帧数={expected_frames}")

    # 创建录制器
    screen_recorder = ScreenRecorder(
        output_path=video_path,
        region=None,  # 全屏录制
        fps=test_fps
    )

    audio_recorder = AudioRecorder(
        output_path=audio_path,
        record_system_audio=True,
        record_microphone=False  # 只录制系统音频，减少干扰
    )

    try:
        # 开始录制
        logger.info("开始录制...")
        start_time = time.time()

        screen_start_success = screen_recorder.start_recording()
        audio_start_success = audio_recorder.start_recording()

        if not screen_start_success:
            logger.error("屏幕录制启动失败")
            return False

        if not audio_start_success:
            logger.error("音频录制启动失败")
            return False

        # 精确录制指定时长
        logger.info(f"精确录制 {test_duration} 秒...")
        time.sleep(test_duration)

        # 停止录制
        logger.info("停止录制...")
        actual_duration = time.time() - start_time
        screen_recorder.stop_recording()
        audio_recorder.stop_recording()

        logger.info(f"实际录制时长: {actual_duration:.2f}秒")

        # 检查文件是否存在
        if not os.path.exists(video_path):
            logger.error(f"视频文件未生成: {video_path}")
            return False

        if not os.path.exists(audio_path):
            logger.error(f"音频文件未生成: {audio_path}")
            return False

        # 获取详细的视频信息
        import cv2
        cap = cv2.VideoCapture(video_path)
        if cap.isOpened():
            video_frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            video_fps = cap.get(cv2.CAP_PROP_FPS)
            video_duration = video_frame_count / video_fps if video_fps > 0 else 0
            cap.release()
        else:
            logger.error("无法读取视频文件信息")
            return False

        # 获取音频时长
        audio_duration = utils.get_media_duration(audio_path)

        # 输出详细结果
        logger.info("=" * 60)
        logger.info("录制结果分析:")
        logger.info(f"  设定FPS: {test_fps}")
        logger.info(f"  录制时长: {test_duration}秒")
        logger.info(f"  期望帧数: {expected_frames}")
        logger.info("-" * 40)
        logger.info(f"  视频文件: {video_path}")
        logger.info(f"  视频帧数: {video_frame_count}")
        logger.info(f"  视频FPS: {video_fps:.2f}")
        logger.info(f"  视频时长: {video_duration:.2f}秒")
        logger.info("-" * 40)
        logger.info(f"  音频文件: {audio_path}")
        logger.info(f"  音频时长: {audio_duration:.2f}秒")
        logger.info("=" * 60)

        # 验证结果
        success = True

        # 1. 检查帧数准确性
        frame_accuracy = (video_frame_count / expected_frames * 100) if expected_frames > 0 else 0
        if frame_accuracy < 90:  # 帧数准确度低于90%
            logger.error(f"❌ 帧数准确度不足: {frame_accuracy:.1f}% (期望≥90%)")
            success = False
        else:
            logger.info(f"✅ 帧数准确度: {frame_accuracy:.1f}%")

        # 2. 检查视频时长准确性
        duration_accuracy = (video_duration / test_duration * 100) if test_duration > 0 else 0
        if abs(video_duration - test_duration) > 1.0:  # 时长误差超过1秒
            logger.error(f"❌ 视频时长误差过大: {abs(video_duration - test_duration):.2f}秒")
            success = False
        else:
            logger.info(f"✅ 视频时长准确度: {duration_accuracy:.1f}%")

        # 3. 检查音视频同步
        if audio_duration:
            sync_diff = abs(video_duration - audio_duration)
            sync_accuracy = (1 - sync_diff / max(video_duration, audio_duration)) * 100
            if sync_diff > 1.0:  # 同步误差超过1秒
                logger.warning(f"⚠️ 音视频同步误差: {sync_diff:.2f}秒")
            else:
                logger.info(f"✅ 音视频同步准确度: {sync_accuracy:.1f}%")

        # 4. 检查FPS准确性
        fps_accuracy = (video_fps / test_fps * 100) if test_fps > 0 else 0
        if abs(video_fps - test_fps) > 2:  # FPS误差超过2
            logger.warning(f"⚠️ FPS误差: 设定{test_fps}, 实际{video_fps:.2f}")
        else:
            logger.info(f"✅ FPS准确度: {fps_accuracy:.1f}%")

        if success:
            logger.info("🎉 录制同步测试完全通过！")
        else:
            logger.error("❌ 录制同步测试失败！")

        return success

    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False
    finally:
        # 清理资源
        try:
            if screen_recorder.is_recording:
                screen_recorder.stop_recording()
            if audio_recorder.is_recording:
                audio_recorder.stop_recording()
        except:
            pass

def test_video_fps_fix():
    """测试视频帧率修复功能"""
    logger.info("开始测试视频帧率修复功能")

    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "test_output")
    os.makedirs(output_dir, exist_ok=True)

    # 生成测试文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    video_path = os.path.join(output_dir, f"test_fps_fix_{timestamp}.mp4")

    # 创建录制器
    screen_recorder = ScreenRecorder(
        output_path=video_path,
        region=None,  # 全屏录制
        fps=30
    )

    try:
        # 开始录制
        logger.info("开始录制...")
        if not screen_recorder.start_recording():
            logger.error("屏幕录制启动失败")
            return False

        # 录制5秒
        test_duration = 5
        logger.info(f"录制 {test_duration} 秒...")
        time.sleep(test_duration)

        # 停止录制
        logger.info("停止录制...")
        screen_recorder.stop_recording()

        # 检查文件是否存在
        if not os.path.exists(video_path):
            logger.error(f"视频文件未生成: {video_path}")
            return False

        # 获取原始视频信息
        original_duration = utils.get_media_duration(video_path)
        logger.info(f"原始视频时长: {original_duration:.2f}秒")

        # 测试帧率修复
        logger.info("测试帧率修复...")
        fixed_path = screen_recorder.fix_video_fps(keep_original=True)

        if fixed_path and os.path.exists(fixed_path):
            fixed_duration = utils.get_media_duration(fixed_path)
            logger.info(f"修复后视频时长: {fixed_duration:.2f}秒")

            # 检查修复效果
            if fixed_duration and original_duration:
                time_diff = abs(fixed_duration - test_duration)
                if time_diff < 1.0:  # 允许1秒误差
                    logger.info("✅ 视频帧率修复测试通过！")
                    return True
                else:
                    logger.warning(f"⚠️ 视频帧率修复测试失败！时长误差: {time_diff:.2f}秒")
                    return False
            else:
                logger.error("无法获取修复后的视频时长")
                return False
        else:
            logger.error("视频帧率修复失败")
            return False

    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        return False
    finally:
        # 清理资源
        try:
            if screen_recorder.is_recording:
                screen_recorder.stop_recording()
        except:
            pass

def main():
    """主函数"""
    logger.info("开始录制修复测试")

    # 测试1: 录制同步
    sync_result = test_recording_sync()

    # 测试2: 视频帧率修复
    fps_result = test_video_fps_fix()

    # 输出测试结果
    logger.info("=" * 50)
    logger.info("测试结果汇总:")
    logger.info(f"  录制同步测试: {'✅ 通过' if sync_result else '❌ 失败'}")
    logger.info(f"  帧率修复测试: {'✅ 通过' if fps_result else '❌ 失败'}")

    if sync_result and fps_result:
        logger.info("🎉 所有测试通过！录制修复成功！")
        return True
    else:
        logger.error("❌ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
