"""
主题管理模块 - 提供主题切换功能
"""

import os
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPalette, QColor

import config

logger = logging.getLogger(__name__)

class ThemeManager:
    """主题管理器"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ThemeManager, cls).__new__(cls)
            cls._instance.init()
        return cls._instance
    
    def init(self):
        """初始化"""
        self.current_theme = config.GUI_CONFIG["theme"]
        logger.info(f"初始化主题管理器: 当前主题={self.current_theme}")
    
    def apply_theme(self, theme_name=None):
        """
        应用主题
        
        参数:
            theme_name (str): 主题名称，如果为None则使用当前主题
        
        返回:
            bool: 是否成功应用主题
        """
        if theme_name is None:
            theme_name = self.current_theme
        
        if theme_name not in ["light", "dark"]:
            logger.error(f"不支持的主题: {theme_name}")
            return False
        
        try:
            if theme_name == "light":
                self._apply_light_theme()
            elif theme_name == "dark":
                self._apply_dark_theme()
            
            self.current_theme = theme_name
            config.GUI_CONFIG["theme"] = theme_name
            
            logger.info(f"已应用主题: {theme_name}")
            return True
        
        except Exception as e:
            logger.error(f"应用主题失败: {str(e)}")
            return False
    
    def _apply_light_theme(self):
        """应用浅色主题"""
        app = QApplication.instance()
        
        # 创建浅色调色板
        palette = QPalette()
        
        # 设置窗口背景色
        palette.setColor(QPalette.Window, QColor(240, 240, 240))
        palette.setColor(QPalette.WindowText, QColor(0, 0, 0))
        
        # 设置按钮颜色
        palette.setColor(QPalette.Button, QColor(240, 240, 240))
        palette.setColor(QPalette.ButtonText, QColor(0, 0, 0))
        
        # 设置基础颜色
        palette.setColor(QPalette.Base, QColor(255, 255, 255))
        palette.setColor(QPalette.AlternateBase, QColor(245, 245, 245))
        
        # 设置文本颜色
        palette.setColor(QPalette.Text, QColor(0, 0, 0))
        palette.setColor(QPalette.BrightText, QColor(255, 255, 255))
        
        # 设置高亮颜色
        palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
        
        # 设置链接颜色
        palette.setColor(QPalette.Link, QColor(0, 0, 255))
        palette.setColor(QPalette.LinkVisited, QColor(128, 0, 128))
        
        # 设置工具提示颜色
        palette.setColor(QPalette.ToolTipBase, QColor(255, 255, 220))
        palette.setColor(QPalette.ToolTipText, QColor(0, 0, 0))
        
        # 应用调色板
        app.setPalette(palette)
        
        # 设置样式表
        app.setStyleSheet("""
            QGroupBox {
                border: 1px solid #CCCCCC;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 3px;
                background-color: #F0F0F0;
            }
            QPushButton {
                background-color: #F0F0F0;
                border: 1px solid #CCCCCC;
                border-radius: 3px;
                padding: 5px;
            }
            QPushButton:hover {
                background-color: #E0E0E0;
            }
            QPushButton:pressed {
                background-color: #D0D0D0;
            }
            QTabWidget::pane {
                border: 1px solid #CCCCCC;
                border-radius: 3px;
            }
            QTabBar::tab {
                background-color: #F0F0F0;
                border: 1px solid #CCCCCC;
                border-bottom-color: #CCCCCC;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                min-width: 8ex;
                padding: 5px;
            }
            QTabBar::tab:selected {
                background-color: #FFFFFF;
                border-bottom-color: #FFFFFF;
            }
            QTabBar::tab:!selected {
                margin-top: 2px;
            }
        """)
    
    def _apply_dark_theme(self):
        """应用深色主题"""
        app = QApplication.instance()
        
        # 创建深色调色板
        palette = QPalette()
        
        # 设置窗口背景色
        palette.setColor(QPalette.Window, QColor(53, 53, 53))
        palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
        
        # 设置按钮颜色
        palette.setColor(QPalette.Button, QColor(53, 53, 53))
        palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
        
        # 设置基础颜色
        palette.setColor(QPalette.Base, QColor(42, 42, 42))
        palette.setColor(QPalette.AlternateBase, QColor(66, 66, 66))
        
        # 设置文本颜色
        palette.setColor(QPalette.Text, QColor(255, 255, 255))
        palette.setColor(QPalette.BrightText, QColor(255, 255, 255))
        
        # 设置高亮颜色
        palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
        
        # 设置链接颜色
        palette.setColor(QPalette.Link, QColor(85, 170, 255))
        palette.setColor(QPalette.LinkVisited, QColor(170, 85, 255))
        
        # 设置工具提示颜色
        palette.setColor(QPalette.ToolTipBase, QColor(53, 53, 53))
        palette.setColor(QPalette.ToolTipText, QColor(255, 255, 255))
        
        # 应用调色板
        app.setPalette(palette)
        
        # 设置样式表
        app.setStyleSheet("""
            QGroupBox {
                border: 1px solid #555555;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 3px;
                background-color: #353535;
            }
            QPushButton {
                background-color: #353535;
                border: 1px solid #555555;
                border-radius: 3px;
                padding: 5px;
            }
            QPushButton:hover {
                background-color: #454545;
            }
            QPushButton:pressed {
                background-color: #252525;
            }
            QTabWidget::pane {
                border: 1px solid #555555;
                border-radius: 3px;
            }
            QTabBar::tab {
                background-color: #353535;
                border: 1px solid #555555;
                border-bottom-color: #555555;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                min-width: 8ex;
                padding: 5px;
            }
            QTabBar::tab:selected {
                background-color: #2A2A2A;
                border-bottom-color: #2A2A2A;
            }
            QTabBar::tab:!selected {
                margin-top: 2px;
            }
        """)
    
    def get_current_theme(self):
        """获取当前主题"""
        return self.current_theme
