"""
音频录制模块 - 负责捕获系统音频和麦克风输入
"""

import os
import time
import wave
import threading
import logging
import pyaudio
import numpy as np
from array import array

import config
import utils

logger = logging.getLogger(__name__)

class AudioRecorder:
    """音频录制类"""

    def __init__(self, output_path=None, sample_rate=None, channels=None,
                 record_system_audio=None, record_microphone=None):
        """
        初始化音频录制器

        参数:
            output_path (str): 输出文件路径，如果为None则使用默认路径
            sample_rate (int): 采样率，如果为None则使用默认采样率
            channels (int): 通道数，如果为None则使用默认通道数
            record_system_audio (bool): 是否录制系统音频，如果为None则使用默认设置
            record_microphone (bool): 是否录制麦克风，如果为None则使用默认设置
        """
        self.output_path = output_path or utils.get_output_file_path(prefix="audio", extension="wav")
        self.sample_rate = sample_rate or config.SCREEN_RECORD_CONFIG["audio_quality"]
        self.channels = channels or config.SCREEN_RECORD_CONFIG["audio_channels"]

        # 录制设置
        self.record_system_audio = record_system_audio
        if self.record_system_audio is None:
            self.record_system_audio = config.SCREEN_RECORD_CONFIG["record_system_audio"]

        self.record_microphone = record_microphone
        if self.record_microphone is None:
            self.record_microphone = config.SCREEN_RECORD_CONFIG["record_microphone"]

        # 如果两者都为False，默认录制麦克风
        if not self.record_system_audio and not self.record_microphone:
            self.record_microphone = True

        # 音频格式
        self.format = pyaudio.paInt16
        self.chunk = 1024

        # 录制状态
        self.is_recording = False
        self.is_paused = False
        self.start_time = 0
        self.elapsed_time = 0
        self.pause_time = 0

        # 录制线程
        self.record_thread = None

        # PyAudio实例
        self.audio = None
        self.stream = None
        self.frames = []

        # 设备索引
        self.input_device_index = self._get_input_device_index()
        self.output_device_index = self._get_output_device_index()

        logger.info(f"初始化音频录制器: 输出路径={self.output_path}, 采样率={self.sample_rate}, "
                   f"通道数={self.channels}, 录制系统音频={self.record_system_audio}, "
                   f"录制麦克风={self.record_microphone}")

    def _get_input_device_index(self):
        """获取输入设备索引"""
        if not self.record_microphone:
            return None

        try:
            p = pyaudio.PyAudio()
            default_input = None

            # 查找默认输入设备
            for i in range(p.get_device_count()):
                device_info = p.get_device_info_by_index(i)
                if device_info["maxInputChannels"] > 0:
                    if default_input is None:
                        default_input = i
                    if device_info.get("isDefaultInputDevice", False):
                        default_input = i
                        break

            p.terminate()
            return default_input

        except Exception as e:
            logger.error(f"获取输入设备索引失败: {str(e)}")
            return None

    def _get_output_device_index(self):
        """获取输出设备索引"""
        if not self.record_system_audio:
            return None

        try:
            p = pyaudio.PyAudio()
            default_output = None

            # 查找默认输出设备
            for i in range(p.get_device_count()):
                device_info = p.get_device_info_by_index(i)
                if device_info["maxOutputChannels"] > 0:
                    if default_output is None:
                        default_output = i
                    if device_info.get("isDefaultOutputDevice", False):
                        default_output = i
                        break

            p.terminate()
            return default_output

        except Exception as e:
            logger.error(f"获取输出设备索引失败: {str(e)}")
            return None

    def start_recording(self):
        """开始录制"""
        if self.is_recording:
            logger.warning("录制已经在进行中")
            return False

        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(self.output_path)
            os.makedirs(output_dir, exist_ok=True)

            # 初始化PyAudio
            self.audio = pyaudio.PyAudio()

            # 打开音频流
            self.stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                output=False,
                frames_per_buffer=self.chunk,
                input_device_index=self.input_device_index
            )

            # 清空帧缓冲
            self.frames = []

            # 设置录制状态
            self.is_recording = True
            self.is_paused = False
            self.start_time = time.time()

            # 启动录制线程
            self.record_thread = threading.Thread(target=self._record_audio)
            self.record_thread.daemon = True
            self.record_thread.start()

            logger.info(f"开始录制音频: {self.output_path}")
            return True

        except Exception as e:
            logger.error(f"开始录制音频失败: {str(e)}")
            if self.audio:
                self.audio.terminate()
                self.audio = None
            return False

    def pause_recording(self):
        """暂停录制"""
        if not self.is_recording:
            logger.warning("没有正在进行的录制")
            return False

        if self.is_paused:
            # 恢复录制
            self.is_paused = False
            self.elapsed_time += time.time() - self.pause_time
            logger.info("恢复录制音频")
            return True
        else:
            # 暂停录制
            self.is_paused = True
            self.pause_time = time.time()
            logger.info("暂停录制音频")
            return True

    def stop_recording(self):
        """停止录制"""
        if not self.is_recording:
            logger.warning("没有正在进行的录制")
            return False

        # 设置录制状态
        self.is_recording = False
        self.is_paused = False

        # 等待录制线程结束
        if self.record_thread and self.record_thread.is_alive():
            self.record_thread.join(timeout=2.0)

        # 关闭流
        if self.stream:
            self.stream.stop_stream()
            self.stream.close()
            self.stream = None

        # 终止PyAudio
        if self.audio:
            self.audio.terminate()
            self.audio = None

        # 保存音频文件
        self._save_audio_file()

        # 计算录制时间
        if not self.is_paused:
            self.elapsed_time += time.time() - self.start_time

        logger.info(f"停止录制音频: {self.output_path}, 录制时间: {utils.format_time(self.elapsed_time)}")
        return True

    def _record_audio(self):
        """录制音频的线程函数"""
        logger.debug("音频录制线程启动")

        try:
            while self.is_recording:
                # 如果暂停，则跳过帧捕获
                if self.is_paused:
                    time.sleep(0.1)
                    continue

                # 读取音频数据
                data = self.stream.read(self.chunk, exception_on_overflow=False)

                # 存储帧
                self.frames.append(data)

        except Exception as e:
            logger.error(f"录制音频过程中发生错误: {str(e)}")
            self.is_recording = False

        finally:
            logger.debug("音频录制线程结束")

    def _save_audio_file(self):
        """保存音频文件"""
        try:
            if not self.frames:
                logger.warning("没有录制到音频数据")
                return False

            # 确保音频对象存在
            if not self.audio:
                self.audio = pyaudio.PyAudio()

            # 创建WAV文件
            with wave.open(self.output_path, 'wb') as wf:
                wf.setnchannels(self.channels)
                # 确保获取采样宽度
                sample_width = self.audio.get_sample_size(self.format)
                wf.setsampwidth(sample_width)
                wf.setframerate(self.sample_rate)
                wf.writeframes(b''.join(self.frames))

            logger.info(f"音频文件已保存: {self.output_path}")
            return True

        except Exception as e:
            logger.error(f"保存音频文件失败: {str(e)}")
            return False

    def get_recording_time(self):
        """获取当前录制时间（秒）"""
        if not self.is_recording:
            return self.elapsed_time

        if self.is_paused:
            return self.elapsed_time

        current_time = time.time()
        return self.elapsed_time + (current_time - self.start_time)
