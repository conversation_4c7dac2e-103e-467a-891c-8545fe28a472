#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的录制测试脚本
专门测试视频录制帧数与时长匹配问题
"""

import os
import sys
import time
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from screen_recorder import ScreenRecorder
import cv2

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def test_frame_accuracy():
    """测试帧数准确性"""
    logger.info("开始测试帧数准确性")
    
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "simple_test_output")
    os.makedirs(output_dir, exist_ok=True)
    
    # 测试参数
    test_fps = 30
    test_duration = 5  # 5秒测试
    expected_frames = test_fps * test_duration  # 期望150帧
    
    # 生成测试文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    video_path = os.path.join(output_dir, f"simple_test_{timestamp}.mp4")
    
    logger.info(f"测试参数: FPS={test_fps}, 时长={test_duration}秒, 期望帧数={expected_frames}")
    logger.info(f"输出文件: {video_path}")
    
    # 创建录制器
    screen_recorder = ScreenRecorder(
        output_path=video_path,
        region=(100, 100, 800, 600),  # 使用较小的区域提高性能
        fps=test_fps
    )
    
    try:
        # 开始录制
        logger.info("开始录制...")
        start_time = time.time()
        
        if not screen_recorder.start_recording():
            logger.error("录制启动失败")
            return False
        
        # 精确等待指定时长
        time.sleep(test_duration)
        
        # 停止录制
        logger.info("停止录制...")
        screen_recorder.stop_recording()
        actual_duration = time.time() - start_time
        
        logger.info(f"实际录制时长: {actual_duration:.2f}秒")
        
        # 等待文件生成
        time.sleep(2)
        
        # 检查文件是否存在
        if not os.path.exists(video_path):
            logger.error(f"视频文件未生成: {video_path}")
            return False
        
        file_size = os.path.getsize(video_path)
        logger.info(f"视频文件大小: {file_size} 字节")
        
        if file_size == 0:
            logger.error("视频文件为空")
            return False
        
        # 获取视频信息
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error("无法打开视频文件")
            return False
        
        video_frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        video_fps = cap.get(cv2.CAP_PROP_FPS)
        video_duration = video_frame_count / video_fps if video_fps > 0 else 0
        cap.release()
        
        # 输出结果
        logger.info("=" * 50)
        logger.info("录制结果:")
        logger.info(f"  设定FPS: {test_fps}")
        logger.info(f"  录制时长: {test_duration}秒")
        logger.info(f"  期望帧数: {expected_frames}")
        logger.info("-" * 30)
        logger.info(f"  实际帧数: {video_frame_count}")
        logger.info(f"  实际FPS: {video_fps:.2f}")
        logger.info(f"  视频时长: {video_duration:.2f}秒")
        logger.info("=" * 50)
        
        # 计算准确度
        frame_accuracy = (video_frame_count / expected_frames * 100) if expected_frames > 0 else 0
        duration_accuracy = (video_duration / test_duration * 100) if test_duration > 0 else 0
        fps_accuracy = (video_fps / test_fps * 100) if test_fps > 0 else 0
        
        logger.info("准确度分析:")
        logger.info(f"  帧数准确度: {frame_accuracy:.1f}%")
        logger.info(f"  时长准确度: {duration_accuracy:.1f}%")
        logger.info(f"  FPS准确度: {fps_accuracy:.1f}%")
        
        # 判断测试结果
        success = True
        
        if frame_accuracy < 90:
            logger.error(f"❌ 帧数准确度不足: {frame_accuracy:.1f}% (期望≥90%)")
            success = False
        else:
            logger.info(f"✅ 帧数准确度合格: {frame_accuracy:.1f}%")
        
        if abs(video_duration - test_duration) > 1.0:
            logger.error(f"❌ 时长误差过大: {abs(video_duration - test_duration):.2f}秒")
            success = False
        else:
            logger.info(f"✅ 时长准确度合格")
        
        if abs(video_fps - test_fps) > 3:
            logger.warning(f"⚠️ FPS误差较大: 设定{test_fps}, 实际{video_fps:.2f}")
        else:
            logger.info(f"✅ FPS准确度合格")
        
        if success:
            logger.info("🎉 测试通过！")
        else:
            logger.error("❌ 测试失败！")
        
        return success
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理资源
        try:
            if screen_recorder.is_recording:
                screen_recorder.stop_recording()
        except:
            pass

def main():
    """主函数"""
    logger.info("开始简化录制测试")
    
    success = test_frame_accuracy()
    
    if success:
        logger.info("✅ 所有测试通过！")
        return True
    else:
        logger.error("❌ 测试失败！")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
