#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建录屏软件图标
"""

import os
from PIL import Image, ImageDraw

def create_icon():
    """创建一个简单的录屏软件图标"""
    # 创建一个透明背景的图像
    size = 256
    image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # 绘制一个红色圆形（录制按钮）
    margin = size // 8
    draw.ellipse(
        [(margin, margin), (size - margin, size - margin)],
        fill=(255, 0, 0, 255)
    )
    
    # 在圆形中间绘制一个白色方块（停止按钮）
    inner_margin = size // 3
    draw.rectangle(
        [(inner_margin, inner_margin), (size - inner_margin, size - inner_margin)],
        fill=(255, 255, 255, 200)
    )
    
    # 保存为ICO文件
    icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "recorder_icon.ico")
    image.save(icon_path, format='ICO')
    print(f"图标已保存到: {icon_path}")
    
    # 保存为PNG文件（作为备用）
    png_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "recorder_icon.png")
    image.save(png_path, format='PNG')
    print(f"PNG图标已保存到: {png_path}")
    
    return icon_path

if __name__ == "__main__":
    try:
        create_icon()
    except Exception as e:
        print(f"创建图标时发生错误: {str(e)}")
