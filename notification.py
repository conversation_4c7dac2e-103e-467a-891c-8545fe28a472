"""
通知模块 - 提供悬浮通知功能
"""

import os
import time
import logging
from PyQt5.QtWidgets import (
    QWidget, QLabel, QPushButton, QHBoxLayout, QVBoxLayout, 
    QApplication, QGraphicsOpacityEffect, QFrame
)
from PyQt5.QtCore import Qt, QTimer, QPropertyAnimation, QPoint, QEasingCurve
from PyQt5.QtGui import QColor, QPalette, QFont

logger = logging.getLogger(__name__)

class NotificationType:
    """通知类型"""
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    PROGRESS = "progress"

class FloatingNotification(QWidget):
    """悬浮通知组件"""
    
    def __init__(self, parent=None, message="", notification_type=NotificationType.INFO, 
                 duration=3000, closable=True, position="bottom_right"):
        """
        初始化悬浮通知
        
        参数:
            parent (QWidget): 父窗口
            message (str): 通知消息
            notification_type (str): 通知类型
            duration (int): 显示时长（毫秒），如果为0则不自动关闭
            closable (bool): 是否可关闭
            position (str): 显示位置 (top_left, top_right, bottom_left, bottom_right, center)
        """
        super().__init__(parent)
        
        self.message = message
        self.notification_type = notification_type
        self.duration = duration
        self.closable = closable
        self.position = position
        self.parent = parent
        
        # 设置窗口属性
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_ShowWithoutActivating)
        
        # 创建UI
        self.setup_ui()
        
        # 设置定时器
        if self.duration > 0:
            self.timer = QTimer(self)
            self.timer.timeout.connect(self.close_animation)
            self.timer.setSingleShot(True)
        
        # 设置动画
        self.opacity_effect = QGraphicsOpacityEffect(self)
        self.opacity_effect.setOpacity(0.0)
        self.setGraphicsEffect(self.opacity_effect)
        
        self.opacity_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.opacity_animation.setDuration(300)
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(1.0)
        self.opacity_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        self.close_opacity_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.close_opacity_animation.setDuration(300)
        self.close_opacity_animation.setStartValue(1.0)
        self.close_opacity_animation.setEndValue(0.0)
        self.close_opacity_animation.setEasingCurve(QEasingCurve.OutCubic)
        self.close_opacity_animation.finished.connect(self.hide)
        
        logger.debug(f"初始化悬浮通知: 消息={message}, 类型={notification_type}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 通知框
        self.notification_frame = QFrame(self)
        self.notification_frame.setObjectName("notificationFrame")
        self.notification_frame.setFrameShape(QFrame.StyledPanel)
        self.notification_frame.setFrameShadow(QFrame.Raised)
        
        # 设置样式
        self.set_style()
        
        # 通知布局
        notification_layout = QHBoxLayout(self.notification_frame)
        notification_layout.setContentsMargins(15, 10, 15, 10)
        notification_layout.setSpacing(10)
        
        # 通知图标
        self.icon_label = QLabel()
        self.icon_label.setFixedSize(24, 24)
        self.set_icon()
        notification_layout.addWidget(self.icon_label)
        
        # 通知消息
        self.message_label = QLabel(self.message)
        self.message_label.setWordWrap(True)
        notification_layout.addWidget(self.message_label)
        
        # 关闭按钮
        if self.closable:
            self.close_button = QPushButton("×")
            self.close_button.setFixedSize(20, 20)
            self.close_button.setObjectName("closeButton")
            self.close_button.clicked.connect(self.close_animation)
            notification_layout.addWidget(self.close_button)
        
        main_layout.addWidget(self.notification_frame)
        
        # 设置大小
        self.setMinimumWidth(300)
        self.setMaximumWidth(400)
        self.adjustSize()
    
    def set_style(self):
        """设置样式"""
        base_style = """
            #notificationFrame {
                border-radius: 6px;
                background-color: #FFFFFF;
                border: 1px solid #DDDDDD;
            }
            #closeButton {
                background-color: transparent;
                border: none;
                color: #666666;
                font-size: 16px;
            }
            #closeButton:hover {
                color: #000000;
            }
        """
        
        type_style = ""
        if self.notification_type == NotificationType.INFO:
            type_style = """
                #notificationFrame {
                    background-color: #E3F2FD;
                    border: 1px solid #90CAF9;
                }
            """
        elif self.notification_type == NotificationType.SUCCESS:
            type_style = """
                #notificationFrame {
                    background-color: #E8F5E9;
                    border: 1px solid #A5D6A7;
                }
            """
        elif self.notification_type == NotificationType.WARNING:
            type_style = """
                #notificationFrame {
                    background-color: #FFF8E1;
                    border: 1px solid #FFE082;
                }
            """
        elif self.notification_type == NotificationType.ERROR:
            type_style = """
                #notificationFrame {
                    background-color: #FFEBEE;
                    border: 1px solid #EF9A9A;
                }
            """
        elif self.notification_type == NotificationType.PROGRESS:
            type_style = """
                #notificationFrame {
                    background-color: #E8EAF6;
                    border: 1px solid #9FA8DA;
                }
            """
        
        self.setStyleSheet(base_style + type_style)
    
    def set_icon(self):
        """设置图标"""
        # 这里可以根据通知类型设置不同的图标
        # 简单起见，这里只使用文字代替图标
        icon_text = "i"
        if self.notification_type == NotificationType.INFO:
            icon_text = "i"
        elif self.notification_type == NotificationType.SUCCESS:
            icon_text = "✓"
        elif self.notification_type == NotificationType.WARNING:
            icon_text = "!"
        elif self.notification_type == NotificationType.ERROR:
            icon_text = "×"
        elif self.notification_type == NotificationType.PROGRESS:
            icon_text = "⟳"
        
        self.icon_label.setText(icon_text)
        self.icon_label.setAlignment(Qt.AlignCenter)
        
        # 设置图标样式
        icon_style = "font-size: 16px; font-weight: bold;"
        if self.notification_type == NotificationType.INFO:
            icon_style += "color: #2196F3;"
        elif self.notification_type == NotificationType.SUCCESS:
            icon_style += "color: #4CAF50;"
        elif self.notification_type == NotificationType.WARNING:
            icon_style += "color: #FFC107;"
        elif self.notification_type == NotificationType.ERROR:
            icon_style += "color: #F44336;"
        elif self.notification_type == NotificationType.PROGRESS:
            icon_style += "color: #3F51B5;"
        
        self.icon_label.setStyleSheet(icon_style)
    
    def show(self):
        """显示通知"""
        # 设置位置
        self.set_position()
        
        # 显示窗口
        super().show()
        
        # 开始显示动画
        self.opacity_animation.start()
        
        # 如果设置了自动关闭，启动定时器
        if self.duration > 0:
            self.timer.start(self.duration)
        
        logger.debug(f"显示悬浮通知: {self.message}")
    
    def set_position(self):
        """设置通知位置"""
        if not self.parent:
            # 如果没有父窗口，则相对于屏幕定位
            screen_geometry = QApplication.desktop().availableGeometry()
            x, y = 0, 0
            
            if self.position == "top_left":
                x = screen_geometry.x() + 20
                y = screen_geometry.y() + 20
            elif self.position == "top_right":
                x = screen_geometry.x() + screen_geometry.width() - self.width() - 20
                y = screen_geometry.y() + 20
            elif self.position == "bottom_left":
                x = screen_geometry.x() + 20
                y = screen_geometry.y() + screen_geometry.height() - self.height() - 20
            elif self.position == "bottom_right":
                x = screen_geometry.x() + screen_geometry.width() - self.width() - 20
                y = screen_geometry.y() + screen_geometry.height() - self.height() - 20
            elif self.position == "center":
                x = screen_geometry.x() + (screen_geometry.width() - self.width()) / 2
                y = screen_geometry.y() + (screen_geometry.height() - self.height()) / 2
            
            self.move(int(x), int(y))
        else:
            # 如果有父窗口，则相对于父窗口定位
            parent_geometry = self.parent.geometry()
            x, y = 0, 0
            
            if self.position == "top_left":
                x = 20
                y = 20
            elif self.position == "top_right":
                x = parent_geometry.width() - self.width() - 20
                y = 20
            elif self.position == "bottom_left":
                x = 20
                y = parent_geometry.height() - self.height() - 20
            elif self.position == "bottom_right":
                x = parent_geometry.width() - self.width() - 20
                y = parent_geometry.height() - self.height() - 20
            elif self.position == "center":
                x = (parent_geometry.width() - self.width()) / 2
                y = (parent_geometry.height() - self.height()) / 2
            
            self.move(self.parent.mapToGlobal(QPoint(int(x), int(y))))
    
    def close_animation(self):
        """关闭动画"""
        self.close_opacity_animation.start()
        logger.debug(f"关闭悬浮通知: {self.message}")
    
    def update_message(self, message):
        """更新通知消息"""
        self.message = message
        self.message_label.setText(message)
        self.adjustSize()
        logger.debug(f"更新悬浮通知消息: {message}")

# 通知管理器
class NotificationManager:
    """通知管理器"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(NotificationManager, cls).__new__(cls)
            cls._instance.init()
        return cls._instance
    
    def init(self):
        """初始化"""
        self.notifications = []
        logger.info("初始化通知管理器")
    
    def show_notification(self, message, notification_type=NotificationType.INFO, 
                         duration=3000, closable=True, position="bottom_right", parent=None):
        """
        显示通知
        
        参数:
            message (str): 通知消息
            notification_type (str): 通知类型
            duration (int): 显示时长（毫秒），如果为0则不自动关闭
            closable (bool): 是否可关闭
            position (str): 显示位置
            parent (QWidget): 父窗口
        
        返回:
            FloatingNotification: 通知对象
        """
        notification = FloatingNotification(
            parent=parent,
            message=message,
            notification_type=notification_type,
            duration=duration,
            closable=closable,
            position=position
        )
        
        self.notifications.append(notification)
        notification.show()
        
        logger.info(f"显示通知: {message}")
        return notification
    
    def clear_all(self):
        """清除所有通知"""
        for notification in self.notifications:
            notification.close_animation()
        
        self.notifications = []
        logger.info("清除所有通知")
